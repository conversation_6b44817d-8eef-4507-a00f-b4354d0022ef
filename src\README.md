# قمة الوعد للسفريات - نظام إدارة متكامل

## نظرة عامة

نظام إدارة شامل ومتطور لوكالات السفر والحج والعمرة، مصمم خصيصاً للسوق السعودي مع دعم كامل للغة العربية واتجاه RTL.

## الميزات الرئيسية

### 🏢 إدارة العملاء
- إدارة بيانات العملاء الشخصية
- تتبع حالة المعاملات والتأشيرات
- سجل كامل للمعاملات والمدفوعات
- تصنيف العملاء حسب النوع والحالة

### 🤝 إدارة الوكلاء
- إدارة شبكة الوكلاء والمندوبين
- نظام العمولات والحوافز
- تتبع الأداء والمبيعات
- تقارير مفصلة للوكلاء

### 🚛 إدارة الموردين
- إدارة قاعدة بيانات الموردين
- تصنيف حسب نوع الخدمة
- متابعة العقود والاتفاقيات
- تقييم الأداء والجودة

### ✈️ إدارة الحجوزات
- حجوزات الطيران والفنادق
- برامج الحج والعمرة
- النقل والمواصلات
- التأمين والخدمات الإضافية

### 📋 مخزون التأشيرات
- إدارة مخزون التأشيرات
- تتبع حالة التأشيرات
- تقارير المخزون والاستهلاك
- تنبيهات انتهاء الصلاحية

### 💰 النظام المحاسبي
- دليل الحسابات المتكامل
- القيود المحاسبية اليومية
- تقارير مالية شاملة
- متابعة الذمم والمدفوعات

### 📊 التقارير والإحصائيات
- تقارير العملاء والوكلاء
- التقارير المالية
- إحصائيات الأداء
- تقارير قابلة للتخصيص

## التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Bootstrap 5.3** - إطار العمل للتصميم
- **Font Awesome 6.5** - الأيقونات
- **Cairo Font** - خط عربي احترافي

### Backend
- **LocalStorage** - تخزين البيانات محلياً
- **JSON** - تنسيق البيانات
- **Web APIs** - واجهات المتصفح

### أدوات التطوير
- **CSS Variables** - نظام المتغيرات
- **Modular JavaScript** - تقسيم الكود لوحدات
- **Responsive Design** - تصميم متجاوب
- **RTL Support** - دعم الاتجاه من اليمين لليسار

## هيكل المشروع

```
src/
├── index.html                 # الصفحة الرئيسية
├── assets/                    # الموارد الثابتة
│   ├── css/                   # ملفات التصميم
│   │   ├── variables.css      # متغيرات CSS
│   │   ├── base.css          # الأنماط الأساسية
│   │   ├── components.css    # مكونات UI
│   │   ├── layout.css        # تخطيط الصفحة
│   │   ├── pages.css         # أنماط الصفحات
│   │   └── responsive.css    # التصميم المتجاوب
│   ├── images/               # الصور والأيقونات
│   └── fonts/                # الخطوط المخصصة
├── js/                       # ملفات JavaScript
│   ├── core/                 # الملفات الأساسية
│   │   ├── app.js           # التطبيق الرئيسي
│   │   ├── database.js      # قاعدة البيانات
│   │   ├── router.js        # نظام التوجيه
│   │   └── auth.js          # نظام المصادقة
│   ├── components/          # مكونات الصفحات
│   │   ├── dashboard.js     # لوحة التحكم
│   │   ├── customers.js     # إدارة العملاء
│   │   ├── suppliers.js     # إدارة الموردين
│   │   ├── agents.js        # إدارة الوكلاء
│   │   ├── bookings.js      # الحجوزات
│   │   ├── inventory.js     # المخزون
│   │   ├── accounting.js    # المحاسبة
│   │   ├── reports.js       # التقارير
│   │   └── settings.js      # الإعدادات
│   └── utils/               # الأدوات المساعدة
│       ├── helpers.js       # دوال مساعدة
│       ├── validation.js    # التحقق من البيانات
│       └── notifications.js # نظام الإشعارات
└── README.md                # هذا الملف
```

## التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث يدعم ES6+
- خادم ويب محلي (اختياري)

### خطوات التثبيت

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd qimat-alwaed
   ```

2. **تشغيل الخادم المحلي** (اختياري)
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # أو باستخدام Node.js
   npx serve .
   
   # أو باستخدام PHP
   php -S localhost:8000
   ```

3. **فتح التطبيق**
   - افتح `index.html` في المتصفح مباشرة
   - أو اذهب إلى `http://localhost:8000`

## الاستخدام

### البدء السريع

1. **تسجيل الدخول**
   - المستخدم الافتراضي: `admin`
   - كلمة المرور: `admin123`

2. **استكشاف لوحة التحكم**
   - عرض الإحصائيات العامة
   - الوصول السريع للوحدات
   - متابعة النشاط الأخير

3. **إدارة البيانات**
   - إضافة العملاء والوكلاء
   - إنشاء الحجوزات
   - تسجيل المعاملات المالية

### الوحدات الرئيسية

#### إدارة العملاء
- إضافة عميل جديد
- تحديث بيانات العملاء
- تتبع حالة المعاملات
- طباعة التقارير

#### إدارة الحجوزات
- إنشاء حجز جديد
- متابعة حالة الحجز
- إدارة المدفوعات
- إرسال التأكيدات

#### النظام المحاسبي
- إنشاء قيد محاسبي
- عرض دليل الحسابات
- تقارير الأرباح والخسائر
- الميزانية العمومية

## التخصيص

### تغيير الألوان
```css
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  /* ... المزيد من المتغيرات */
}
```

### إضافة وحدة جديدة
1. إنشاء ملف JavaScript في `js/components/`
2. إضافة الأنماط في `assets/css/`
3. تحديث نظام التوجيه
4. إضافة رابط في شريط التنقل

### تخصيص التقارير
- تعديل قوالب التقارير
- إضافة حقول جديدة
- تخصيص التصدير

## الأمان

### حماية البيانات
- تشفير البيانات الحساسة
- التحقق من صحة المدخلات
- حماية من XSS و CSRF

### النسخ الاحتياطية
- نسخ احتياطية تلقائية
- تصدير البيانات
- استعادة النظام

## الدعم والمساعدة

### المشاكل الشائعة
- **البيانات لا تحفظ**: تأكد من تمكين JavaScript
- **التصميم لا يظهر**: تحقق من تحميل ملفات CSS
- **الأخطاء في وحدة التحكم**: راجع ملفات JavaScript

### التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX
- الموقع: www.qimat-alwaed.com

## الترخيص

هذا المشروع محمي بحقوق الطبع والنشر © 2024 قمة الوعد للسفريات. جميع الحقوق محفوظة.

## سجل التغييرات

### الإصدار 2.0.0 (2024-01-XX)
- إعادة تصميم كاملة للواجهة
- تحسين الأداء والاستجابة
- إضافة نظام الإشعارات
- تحسين نظام قاعدة البيانات
- دعم أفضل للأجهزة المحمولة

### الإصدار 1.0.0 (2023-XX-XX)
- الإصدار الأولي
- الوحدات الأساسية
- النظام المحاسبي
- إدارة العملاء والوكلاء

---

**تم تطوير هذا النظام بواسطة فريق قمة الوعد للسفريات**

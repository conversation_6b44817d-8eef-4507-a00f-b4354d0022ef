<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قالب التقرير المالي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20mm;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }
        
        .report-header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px 10px 0 0;
            position: relative;
            overflow: hidden;
        }
        
        .report-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
            opacity: 0.3;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .company-info {
            position: relative;
            z-index: 2;
        }
        
        .company-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .company-details {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 1rem;
        }
        
        .report-title {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 3px solid #1e293b;
            text-align: center;
        }
        
        .report-title h2 {
            color: #1e293b;
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
        }
        
        .report-period {
            color: #64748b;
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .report-body {
            padding: 2rem;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #64748b);
        }
        
        .summary-card.revenue {
            --card-color: #10b981;
        }
        
        .summary-card.expenses {
            --card-color: #ef4444;
        }
        
        .summary-card.profit {
            --card-color: #6366f1;
        }
        
        .summary-card.balance {
            --card-color: #f59e0b;
        }
        
        .card-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 0.5rem;
        }
        
        .card-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--card-color, #64748b);
            margin-bottom: 0.5rem;
        }
        
        .card-currency {
            font-size: 0.9rem;
            color: #94a3b8;
        }
        
        .financial-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .table-header {
            background: #1e293b;
            color: white;
            padding: 1rem;
            font-weight: 600;
            text-align: center;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #f1f5f9;
            color: #334155;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
            border-bottom: 2px solid #e2e8f0;
        }
        
        td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #f1f5f9;
        }
        
        tr:hover {
            background: #f8fafc;
        }
        
        .amount-positive {
            color: #10b981;
            font-weight: 600;
        }
        
        .amount-negative {
            color: #ef4444;
            font-weight: 600;
        }
        
        .amount-neutral {
            color: #64748b;
            font-weight: 600;
        }
        
        .chart-section {
            background: #f8fafc;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        .chart-placeholder {
            height: 300px;
            background: white;
            border: 2px dashed #cbd5e1;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #94a3b8;
            font-size: 1.1rem;
        }
        
        .notes-section {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .notes-title {
            font-weight: 700;
            color: #92400e;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .notes-content {
            color: #451a03;
            line-height: 1.8;
        }
        
        .report-footer {
            background: #1e293b;
            color: white;
            padding: 2rem;
            border-radius: 0 0 10px 10px;
            text-align: center;
        }
        
        .footer-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .footer-item {
            text-align: center;
        }
        
        .footer-label {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-bottom: 0.25rem;
        }
        
        .footer-value {
            font-weight: 600;
        }
        
        .print-date {
            position: absolute;
            top: 10mm;
            left: 10mm;
            font-size: 0.8rem;
            color: #64748b;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .print-date {
                display: block;
            }
            
            .chart-placeholder {
                height: 200px;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            .company-name {
                font-size: 2rem;
            }
            
            .card-value {
                font-size: 1.5rem;
            }
            
            .table-content {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="print-date">
        تاريخ الطباعة: <span id="printDate"></span>
    </div>
    
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <div class="company-info">
                <h1 class="company-name">شركة قيمة الوعد للسفريات</h1>
                <p class="company-details">
                    المملكة العربية السعودية - الرياض<br>
                    هاتف: +966 11 123 4567 | البريد: <EMAIL>
                </p>
            </div>
        </div>
        
        <!-- عنوان التقرير -->
        <div class="report-title">
            <h2>التقرير المالي الشهري</h2>
            <p class="report-period">يناير 2024</p>
        </div>
        
        <!-- محتوى التقرير -->
        <div class="report-body">
            <!-- بطاقات الملخص -->
            <div class="summary-cards">
                <div class="summary-card revenue">
                    <div class="card-title">إجمالي الإيرادات</div>
                    <div class="card-value">245,750</div>
                    <div class="card-currency">ريال سعودي</div>
                </div>
                <div class="summary-card expenses">
                    <div class="card-title">إجمالي المصروفات</div>
                    <div class="card-value">189,250</div>
                    <div class="card-currency">ريال سعودي</div>
                </div>
                <div class="summary-card profit">
                    <div class="card-title">صافي الربح</div>
                    <div class="card-value">56,500</div>
                    <div class="card-currency">ريال سعودي</div>
                </div>
                <div class="summary-card balance">
                    <div class="card-title">الرصيد النقدي</div>
                    <div class="card-value">125,750</div>
                    <div class="card-currency">ريال سعودي</div>
                </div>
            </div>
            
            <!-- جدول تفصيل الإيرادات -->
            <div class="financial-table">
                <div class="table-header">تفصيل الإيرادات</div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th>نوع الخدمة</th>
                                <th>عدد العمليات</th>
                                <th>المبلغ</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>حجوزات الطيران</td>
                                <td>156</td>
                                <td class="amount-positive">125,500 ر.س</td>
                                <td>51%</td>
                            </tr>
                            <tr>
                                <td>حجوزات الفنادق</td>
                                <td>89</td>
                                <td class="amount-positive">78,250 ر.س</td>
                                <td>32%</td>
                            </tr>
                            <tr>
                                <td>رحلات العمرة</td>
                                <td>12</td>
                                <td class="amount-positive">32,000 ر.س</td>
                                <td>13%</td>
                            </tr>
                            <tr>
                                <td>خدمات أخرى</td>
                                <td>45</td>
                                <td class="amount-positive">10,000 ر.س</td>
                                <td>4%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- جدول تفصيل المصروفات -->
            <div class="financial-table">
                <div class="table-header">تفصيل المصروفات</div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th>نوع المصروف</th>
                                <th>المبلغ</th>
                                <th>النسبة</th>
                                <th>مقارنة بالشهر السابق</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>رواتب الموظفين</td>
                                <td class="amount-negative">85,000 ر.س</td>
                                <td>45%</td>
                                <td class="amount-neutral">0%</td>
                            </tr>
                            <tr>
                                <td>إيجار المكتب</td>
                                <td class="amount-negative">25,000 ر.س</td>
                                <td>13%</td>
                                <td class="amount-neutral">0%</td>
                            </tr>
                            <tr>
                                <td>تكاليف التسويق</td>
                                <td class="amount-negative">35,250 ر.س</td>
                                <td>19%</td>
                                <td class="amount-positive">+15%</td>
                            </tr>
                            <tr>
                                <td>مصروفات إدارية</td>
                                <td class="amount-negative">22,000 ر.س</td>
                                <td>12%</td>
                                <td class="amount-negative">-8%</td>
                            </tr>
                            <tr>
                                <td>مصروفات أخرى</td>
                                <td class="amount-negative">22,000 ر.س</td>
                                <td>11%</td>
                                <td class="amount-positive">+5%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- قسم الرسم البياني -->
            <div class="chart-section">
                <div class="chart-title">مقارنة الإيرادات والمصروفات</div>
                <div class="chart-placeholder">
                    سيتم إدراج الرسم البياني هنا
                </div>
            </div>
            
            <!-- قسم الملاحظات -->
            <div class="notes-section">
                <div class="notes-title">ملاحظات مهمة:</div>
                <div class="notes-content">
                    • تم تحقيق نمو في الإيرادات بنسبة 12% مقارنة بالشهر السابق<br>
                    • زيادة في تكاليف التسويق نتيجة لحملة إعلانية جديدة<br>
                    • انخفاض في المصروفات الإدارية بسبب تحسين الكفاءة التشغيلية<br>
                    • توقع استمرار النمو في الشهر القادم مع بداية موسم العمرة
                </div>
            </div>
        </div>
        
        <!-- ذيل التقرير -->
        <div class="report-footer">
            <div class="footer-info">
                <div class="footer-item">
                    <div class="footer-label">تاريخ إعداد التقرير</div>
                    <div class="footer-value">31 يناير 2024</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">معد التقرير</div>
                    <div class="footer-value">قسم المحاسبة</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">مراجع التقرير</div>
                    <div class="footer-value">المدير المالي</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">معتمد من</div>
                    <div class="footer-value">المدير العام</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // تحديث تاريخ الطباعة
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-SA');
    </script>
</body>
</html>

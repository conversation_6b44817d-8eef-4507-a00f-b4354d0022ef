/**
 * ===================================
 * التطبيق الرئيسي - Main Application
 * ===================================
 */

// إنشاء كائن التطبيق الرئيسي
window.App = {
    // إعدادات التطبيق
    config: {
        name: 'قمة الوعد للسفريات',
        version: '2.0.0',
        debug: true,
        apiUrl: '',
        storagePrefix: 'qimat_alwaed_',
        defaultLanguage: 'ar',
        theme: 'light'
    },

    // حالة التطبيق
    state: {
        isInitialized: false,
        currentUser: null,
        currentPage: 'dashboard',
        isLoading: false,
        notifications: []
    },

    // عناصر DOM المهمة
    elements: {
        loadingScreen: null,
        mainContent: null,
        navbar: null,
        footer: null
    },

    /**
     * تهيئة التطبيق
     */
    init: function() {
        console.log(`🚀 تهيئة ${this.config.name} v${this.config.version}`);
        
        try {
            // تهيئة نظام إدارة الحالة
            if (window.State) {
                window.State.init();
                this.state = window.State;
            }

            // تهيئة نظام الأداء
            if (window.Performance) {
                window.Performance.init();
            }

            // تهيئة نظام التخزين المؤقت
            if (window.Cache) {
                window.Cache.init();
            }

            // تهيئة نظام الأمان
            if (window.Security) {
                window.Security.init();
            }

            // تهيئة نظام واجهة المستخدم
            if (window.UI) {
                window.UI.init();
            }

            // تهيئة نظام النوافذ المنبثقة
            if (window.Modals) {
                window.Modals.init();
            }

            // تهيئة نظام التنقل المحسن
            if (window.Navigation) {
                window.Navigation.init();
            }

            // تهيئة نظام البحث المتقدم
            if (window.Search) {
                window.Search.init();
            }

            // تهيئة نظام التصدير
            if (window.Export) {
                window.Export.init();
            }

            // تهيئة نظام التقارير
            if (window.Reports) {
                window.Reports.init();
            }

            // تهيئة نظام الاختبار (في وضع التطوير فقط)
            if (window.Testing && this.config.debug) {
                window.Testing.init();
            }

            // تهيئة النظام المحاسبي
            if (window.Accounting) {
                window.Accounting.init();
            }

            // تهيئة مكون النظام المحاسبي
            if (window.AccountingComponent) {
                window.AccountingComponent.init();
            }

            // تهيئة عناصر DOM
            this.initElements();

            // تهيئة قاعدة البيانات
            this.initDatabase();

            // إنشاء فهارس قاعدة البيانات
            this.createDatabaseIndexes();

            // تهيئة المصادقة
            this.initAuth();

            // تهيئة الموجه
            this.initRouter();

            // تهيئة الأحداث
            this.initEvents();

            // تحميل الصفحة الافتراضية
            this.loadDefaultPage();

            // إخفاء شاشة التحميل
            this.hideLoadingScreen();

            // بدء مراقبة الأداء
            this.startPerformanceMonitoring();

            // تحديث حالة التطبيق
            if (this.state && this.state.set) {
                this.state.set('app.isInitialized', true);
                this.state.set('app.isLoading', false);
            } else {
                this.state.isInitialized = true;
            }
            
            console.log('✅ تم تهيئة التطبيق بنجاح');
            
            // إرسال حدث تهيئة التطبيق
            this.emit('app:initialized');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق:', error);
            this.showError('حدث خطأ في تهيئة التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    },

    /**
     * تهيئة عناصر DOM
     */
    initElements: function() {
        this.elements.loadingScreen = document.getElementById('loading-screen');
        this.elements.mainContent = document.getElementById('main-content');
        this.elements.navbar = document.getElementById('main-navbar');
        this.elements.footer = document.querySelector('.main-footer');
        
        if (!this.elements.mainContent) {
            throw new Error('عنصر المحتوى الرئيسي غير موجود');
        }
    },

    /**
     * تهيئة قاعدة البيانات
     */
    initDatabase: function() {
        if (window.Database && typeof window.Database.init === 'function') {
            window.Database.init();
            console.log('✅ تم تهيئة قاعدة البيانات');
        } else {
            console.warn('⚠️ قاعدة البيانات غير متوفرة');
        }
    },

    /**
     * تهيئة نظام المصادقة
     */
    initAuth: function() {
        if (window.Auth && typeof window.Auth.init === 'function') {
            window.Auth.init();
            this.state.currentUser = window.Auth.getCurrentUser();
            console.log('✅ تم تهيئة نظام المصادقة');
        } else {
            console.warn('⚠️ نظام المصادقة غير متوفر');
        }
    },

    /**
     * تهيئة نظام التوجيه
     */
    initRouter: function() {
        if (window.Router && typeof window.Router.init === 'function') {
            window.Router.init();
            console.log('✅ تم تهيئة نظام التوجيه');
        } else {
            console.warn('⚠️ نظام التوجيه غير متوفر');
        }
    },

    /**
     * تهيئة الأحداث
     */
    initEvents: function() {
        // أحداث النافذة
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        
        // أحداث لوحة المفاتيح
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // أحداث النقر
        document.addEventListener('click', this.handleClick.bind(this));
        
        console.log('✅ تم تهيئة الأحداث');
    },

    /**
     * تحميل الصفحة الافتراضية
     */
    loadDefaultPage: function() {
        // تحديد الصفحة من الرابط أو استخدام الافتراضية
        const hash = window.location.hash.substring(1);
        const page = hash || 'dashboard';
        
        this.navigateTo(page);
    },

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingScreen: function() {
        if (this.elements.loadingScreen) {
            setTimeout(() => {
                this.elements.loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    this.elements.loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        }
    },

    /**
     * التنقل إلى صفحة
     */
    navigateTo: function(page, params = {}) {
        try {
            console.log(`🔄 التنقل إلى صفحة: ${page}`);
            
            // تحديث حالة التطبيق
            this.state.currentPage = page;
            
            // تحديث الرابط
            window.location.hash = page;
            
            // تحديث شريط التنقل
            this.updateNavbar(page);
            
            // تحميل محتوى الصفحة
            this.loadPageContent(page, params);
            
            // إرسال حدث التنقل
            this.emit('app:navigate', { page, params });
            
        } catch (error) {
            console.error('❌ خطأ في التنقل:', error);
            this.showError('حدث خطأ في التنقل');
        }
    },

    /**
     * تحديث شريط التنقل
     */
    updateNavbar: function(currentPage) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            // تحديد الرابط النشط بناءً على الصفحة الحالية
            const href = link.getAttribute('onclick');
            if (href && href.includes(currentPage)) {
                link.classList.add('active');
            }
        });
    },

    /**
     * تحميل محتوى الصفحة
     */
    loadPageContent: function(page, params = {}) {
        // إظهار مؤشر التحميل
        this.showLoading();
        
        // تحديد مكون الصفحة
        const componentName = this.getComponentName(page);
        const component = window[componentName];
        
        if (component && typeof component.render === 'function') {
            // تحميل المكون
            const content = component.render(params);
            this.elements.mainContent.innerHTML = content;
            
            // تهيئة المكون إذا كان له دالة init
            if (typeof component.init === 'function') {
                component.init(params);
            }
        } else {
            // صفحة غير موجودة
            this.show404();
        }
        
        // إخفاء مؤشر التحميل
        this.hideLoading();
    },

    /**
     * الحصول على اسم المكون من اسم الصفحة
     */
    getComponentName: function(page) {
        const componentMap = {
            'dashboard': 'Dashboard',
            'customers': 'Customers',
            'suppliers': 'Suppliers',
            'agents': 'Agents',
            'bookings': 'Bookings',
            'inventory': 'Inventory',
            'accounting': 'Accounting',
            'reports': 'Reports',
            'settings': 'Settings'
        };
        
        return componentMap[page] || 'Dashboard';
    },

    /**
     * عرض صفحة 404
     */
    show404: function() {
        this.elements.mainContent.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <h1>404</h1>
                    <h2>الصفحة غير موجودة</h2>
                    <p>عذراً، الصفحة التي تبحث عنها غير موجودة.</p>
                    <button class="btn btn-primary" onclick="App.navigateTo('dashboard')">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoading: function() {
        this.state.isLoading = true;
        // يمكن إضافة مؤشر تحميل هنا
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading: function() {
        this.state.isLoading = false;
        // يمكن إخفاء مؤشر التحميل هنا
    },

    /**
     * عرض رسالة خطأ
     */
    showError: function(message) {
        if (window.Notifications && typeof window.Notifications.error === 'function') {
            window.Notifications.error(message);
        } else {
            alert(message);
        }
    },

    /**
     * إرسال حدث مخصص
     */
    emit: function(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    },

    /**
     * الاستماع لحدث مخصص
     */
    on: function(eventName, callback) {
        document.addEventListener(eventName, callback);
    },

    /**
     * معالج تغيير حجم النافذة
     */
    handleResize: function() {
        // يمكن إضافة منطق تغيير حجم النافذة هنا
    },

    /**
     * معالج قبل إغلاق النافذة
     */
    handleBeforeUnload: function(event) {
        // حفظ البيانات قبل الإغلاق
        if (window.Database && typeof window.Database.save === 'function') {
            window.Database.save();
        }
    },

    /**
     * معالج أحداث لوحة المفاتيح
     */
    handleKeydown: function(event) {
        // اختصارات لوحة المفاتيح
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 's':
                    event.preventDefault();
                    // حفظ البيانات
                    break;
                case 'f':
                    event.preventDefault();
                    // فتح البحث
                    break;
            }
        }
    },

    /**
     * معالج أحداث النقر
     */
    handleClick: function(event) {
        // يمكن إضافة منطق النقر العام هنا
    },

    /**
     * إنشاء فهارس قاعدة البيانات
     */
    createDatabaseIndexes: function() {
        if (!window.Database) return;

        try {
            // إنشاء فهارس للبحث السريع
            window.Database.createIndex('customers', 'name');
            window.Database.createIndex('customers', 'phone');
            window.Database.createIndex('customers', 'customer_code');

            window.Database.createIndex('suppliers', 'name');
            window.Database.createIndex('suppliers', 'supplier_type');

            window.Database.createIndex('agents', 'name');
            window.Database.createIndex('agents', 'agent_code');

            window.Database.createIndex('bookings', 'booking_number');
            window.Database.createIndex('bookings', 'customer_name');

            console.log('📊 تم إنشاء فهارس قاعدة البيانات');
        } catch (error) {
            console.error('❌ خطأ في إنشاء فهارس قاعدة البيانات:', error);
        }
    },

    /**
     * بدء مراقبة الأداء
     */
    startPerformanceMonitoring: function() {
        if (!window.Performance) return;

        try {
            // مراقبة تحميل المكونات
            document.addEventListener('router:navigate', (event) => {
                const componentName = event.detail.route?.component;
                if (componentName) {
                    window.Performance.measure(`component-load-${componentName}`, () => {
                        // سيتم قياس وقت تحميل المكون تلقائياً
                    });
                }
            });

            console.log('📈 تم بدء مراقبة الأداء');
        } catch (error) {
            console.error('❌ خطأ في بدء مراقبة الأداء:', error);
        }
    },

    /**
     * تحسين الأداء
     */
    optimizePerformance: function() {
        try {
            // تحسين قاعدة البيانات
            if (window.Database && window.Database.compressData) {
                window.Database.compressData();
            }

            // تحسين التخزين المؤقت
            if (window.Cache && window.Cache.optimize) {
                window.Cache.optimize();
            }

            console.log('🔧 تم تحسين أداء التطبيق');

            if (window.Notifications) {
                window.Notifications.success('تم تحسين أداء التطبيق بنجاح');
            }
        } catch (error) {
            console.error('❌ خطأ في تحسين الأداء:', error);
        }
    },

    /**
     * الحصول على تقرير الأداء
     */
    getPerformanceReport: function() {
        const report = {
            timestamp: new Date().toISOString(),
            app: {
                version: this.config.version,
                uptime: Date.now() - this.startTime,
                isInitialized: this.state.isInitialized || false
            }
        };

        // إضافة تقرير الأداء
        if (window.Performance && window.Performance.getReport) {
            report.performance = window.Performance.getReport();
        }

        // إضافة إحصائيات قاعدة البيانات
        if (window.Database && window.Database.getStats) {
            report.database = window.Database.getStats();
        }

        // إضافة إحصائيات التخزين المؤقت
        if (window.Cache && window.Cache.getStats) {
            report.cache = window.Cache.getStats();
        }

        return report;
    },

    /**
     * تصدير بيانات التطبيق
     */
    exportAppData: function() {
        const exportData = {
            timestamp: new Date().toISOString(),
            version: this.config.version,
            data: {}
        };

        // تصدير بيانات قاعدة البيانات
        if (window.Database && window.Database.exportData) {
            exportData.data.database = JSON.parse(window.Database.exportData());
        }

        // تصدير حالة التطبيق
        if (this.state && this.state.export) {
            exportData.data.state = this.state.export();
        }

        return JSON.stringify(exportData, null, 2);
    },

    /**
     * استيراد بيانات التطبيق
     */
    importAppData: function(jsonData) {
        try {
            const importData = JSON.parse(jsonData);

            // استيراد بيانات قاعدة البيانات
            if (importData.data?.database && window.Database) {
                window.Database.importData(JSON.stringify(importData.data.database));
            }

            // استيراد حالة التطبيق
            if (importData.data?.state && this.state && this.state.import) {
                this.state.import(importData.data.state);
            }

            console.log('✅ تم استيراد بيانات التطبيق بنجاح');

            if (window.Notifications) {
                window.Notifications.success('تم استيراد البيانات بنجاح');
            }

            return true;
        } catch (error) {
            console.error('❌ خطأ في استيراد البيانات:', error);

            if (window.Notifications) {
                window.Notifications.error('فشل في استيراد البيانات');
            }

            return false;
        }
    }
};

// تصدير التطبيق للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
}

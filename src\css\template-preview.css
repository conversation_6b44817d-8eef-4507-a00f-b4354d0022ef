/* ===== متغيرات CSS ===== */
:root {
    --preview-bg: #f8fafc;
    --preview-toolbar-bg: #ffffff;
    --preview-border: #e2e8f0;
    --preview-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --preview-text: #334155;
    --preview-accent: #3b82f6;
    --preview-success: #10b981;
    --preview-warning: #f59e0b;
    --preview-danger: #ef4444;
}

/* ===== التخطيط العام ===== */
body {
    font-family: 'Cairo', sans-serif;
    background: var(--preview-bg);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* شريط التقدم للتمرير */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--preview-accent), var(--preview-success));
    z-index: 9999;
    transition: width 0.3s ease;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    box-shadow: var(--preview-shadow);
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* ===== حاوي المعاينة ===== */
.preview-container {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 76px);
}

/* شريط الأدوات */
.preview-toolbar {
    background: var(--preview-toolbar-bg);
    border-bottom: 1px solid var(--preview-border);
    padding: 1rem;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.toolbar-section {
    flex: 1;
    min-width: 300px;
}

.toolbar-section h5 {
    color: var(--preview-text);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.toolbar-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
}

.control-group label {
    font-size: 0.9rem;
    color: var(--preview-text);
    white-space: nowrap;
    min-width: 80px;
}

.form-range {
    width: 100px;
}

#zoomValue {
    font-weight: 600;
    color: var(--preview-accent);
    min-width: 40px;
    text-align: center;
}

/* ===== منطقة المعاينة ===== */
.preview-area {
    flex: 1;
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    background: var(--preview-bg);
    position: relative;
}

.preview-page {
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    max-width: 100%;
}

.preview-page:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.template-content {
    width: 210mm;
    min-height: 297mm;
    padding: 20mm;
    box-sizing: border-box;
    position: relative;
}

/* ===== تصميم السند ===== */
.voucher-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.voucher-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 10px 10px 0 0;
    position: relative;
    overflow: hidden;
}

.voucher-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    opacity: 0.3;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.company-info {
    position: relative;
    z-index: 2;
}

.company-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.company-details {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    line-height: 1.6;
}

.voucher-title {
    background: #f8fafc;
    padding: 1.5rem;
    border-bottom: 3px solid #1e293b;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-title h2 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.voucher-number {
    background: #1e293b;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.voucher-body {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* معلومات السند */
.voucher-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-weight: 600;
    color: #64748b;
    font-size: 0.9rem;
}

.info-value {
    font-weight: 700;
    color: #1e293b;
    font-size: 1.1rem;
    padding: 0.75rem;
    background: #f1f5f9;
    border-radius: 8px;
    border-right: 4px solid var(--preview-accent);
}

/* قسم المبلغ */
.amount-section {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.amount-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 15s infinite linear reverse;
}

.amount-label {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.amount-value {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 2;
}

.amount-currency {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* المبلغ بالأحرف */
.amount-words {
    background: #fef3c7;
    border: 2px solid #f59e0b;
    border-radius: 10px;
    padding: 1.5rem;
}

.amount-words-label {
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.5rem;
}

.amount-words-value {
    font-weight: 700;
    color: #451a03;
    font-size: 1.1rem;
}

/* وصف السند */
.description-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1.5rem;
}

.description-label {
    font-weight: 600;
    color: #64748b;
    margin-bottom: 1rem;
}

.description-value {
    color: #1e293b;
    line-height: 1.8;
    font-size: 1rem;
}

/* التوقيعات */
.signatures-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.signature-box {
    text-align: center;
}

.signature-title {
    font-weight: 600;
    color: #64748b;
    margin-bottom: 2rem;
}

.signature-line {
    height: 2px;
    background: #cbd5e1;
    margin-bottom: 0.5rem;
}

/* ذيل السند */
.voucher-footer {
    background: #1e293b;
    color: white;
    padding: 1.5rem;
    text-align: center;
    border-radius: 0 0 10px 10px;
}

.footer-text {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ===== أوضاع العرض ===== */
.preview-area.fit-width .template-content {
    width: calc(100vw - 4rem);
    max-width: 210mm;
}

.preview-area.fit-page .preview-page {
    width: calc(100vw - 4rem);
    height: calc(100vh - 200px);
}

.preview-area.actual-size .template-content {
    width: 210mm;
    min-height: 297mm;
}

/* ===== خلفيات العرض ===== */
.preview-area.bg-gray {
    background: #f1f5f9;
}

.preview-area.bg-dark {
    background: #1e293b;
}

/* ===== المساطر والشبكة ===== */
.preview-area.show-rulers::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(90deg, transparent 0%, transparent 9mm, #cbd5e1 9mm, #cbd5e1 10mm, transparent 10mm);
    background-size: 10mm 100%;
    z-index: 10;
}

.preview-area.show-grid .template-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(203, 213, 225, 0.3) 1px, transparent 1px),
        linear-gradient(90deg, rgba(203, 213, 225, 0.3) 1px, transparent 1px);
    background-size: 10mm 10mm;
    pointer-events: none;
    z-index: 1;
}

.preview-area.show-margins .template-content::after {
    content: '';
    position: absolute;
    top: 20mm;
    left: 20mm;
    right: 20mm;
    bottom: 20mm;
    border: 2px dashed #f59e0b;
    pointer-events: none;
    z-index: 2;
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .preview-toolbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .toolbar-section {
        min-width: auto;
    }
    
    .toolbar-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        min-width: auto;
        flex-direction: column;
        align-items: stretch;
    }
    
    .preview-area {
        padding: 1rem;
    }
    
    .template-content {
        width: 100%;
        min-height: auto;
        padding: 10mm;
    }
    
    .voucher-info {
        grid-template-columns: 1fr;
    }
    
    .signatures-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .company-name {
        font-size: 1.8rem;
    }
    
    .amount-value {
        font-size: 2rem;
    }
}

/* ===== طباعة ===== */
@media print {
    body {
        margin: 0;
        padding: 0;
        background: white;
    }
    
    .navbar,
    .preview-toolbar {
        display: none;
    }
    
    .preview-container {
        min-height: auto;
    }
    
    .preview-area {
        padding: 0;
        background: white;
    }
    
    .preview-page {
        box-shadow: none;
        border-radius: 0;
        width: 100%;
        height: 100%;
    }
    
    .template-content {
        width: 100%;
        min-height: 100vh;
        padding: 0;
    }
    
    .voucher-container {
        page-break-inside: avoid;
    }
}

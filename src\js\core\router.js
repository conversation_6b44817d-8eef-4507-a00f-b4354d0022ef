/**
 * ===================================
 * نظام التوجيه - Router System
 * ===================================
 */

window.Router = {
    // إعدادات التوجيه
    config: {
        defaultRoute: 'dashboard',
        hashPrefix: '#',
        enableHistory: true,
        scrollToTop: true
    },

    // المسارات المسجلة
    routes: new Map(),

    // المسار الحالي
    currentRoute: null,

    // معاملات المسار الحالي
    currentParams: {},

    // تاريخ التنقل
    history: [],

    // حالة التوجيه
    state: {
        isInitialized: false,
        isNavigating: false
    },

    /**
     * تهيئة نظام التوجيه
     */
    init: function() {
        console.log('🔄 تهيئة نظام التوجيه');
        
        try {
            // تسجيل المسارات الافتراضية
            this.registerDefaultRoutes();
            
            // إضافة مستمعي الأحداث
            this.addEventListeners();
            
            // تحميل المسار الحالي
            this.loadCurrentRoute();
            
            // تحديث الحالة
            this.state.isInitialized = true;
            
            console.log('✅ تم تهيئة نظام التوجيه بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التوجيه:', error);
        }
    },

    /**
     * تسجيل المسارات الافتراضية
     */
    registerDefaultRoutes: function() {
        // المسارات الأساسية
        this.register('dashboard', {
            component: 'Dashboard',
            title: 'لوحة التحكم',
            icon: 'fas fa-home',
            requiresAuth: false
        });

        this.register('customers', {
            component: 'Customers',
            title: 'إدارة العملاء',
            icon: 'fas fa-users',
            requiresAuth: false
        });

        this.register('suppliers', {
            component: 'Suppliers',
            title: 'إدارة الموردين',
            icon: 'fas fa-truck',
            requiresAuth: false
        });

        this.register('agents', {
            component: 'Agents',
            title: 'إدارة الوكلاء',
            icon: 'fas fa-handshake',
            requiresAuth: false
        });

        this.register('bookings', {
            component: 'Bookings',
            title: 'الحجوزات',
            icon: 'fas fa-calendar-check',
            requiresAuth: false
        });

        this.register('inventory', {
            component: 'Inventory',
            title: 'المخزون',
            icon: 'fas fa-boxes',
            requiresAuth: false
        });

        this.register('accounting', {
            component: 'Accounting',
            title: 'الحسابات',
            icon: 'fas fa-calculator',
            requiresAuth: false
        });

        this.register('reports', {
            component: 'Reports',
            title: 'التقارير',
            icon: 'fas fa-chart-bar',
            requiresAuth: false
        });

        this.register('settings', {
            component: 'Settings',
            title: 'الإعدادات',
            icon: 'fas fa-cog',
            requiresAuth: false
        });

        console.log(`📋 تم تسجيل ${this.routes.size} مسار`);
    },

    /**
     * تسجيل مسار جديد
     */
    register: function(path, options = {}) {
        const route = {
            path: path,
            component: options.component || null,
            title: options.title || path,
            icon: options.icon || 'fas fa-circle',
            requiresAuth: options.requiresAuth !== false,
            beforeEnter: options.beforeEnter || null,
            afterEnter: options.afterEnter || null,
            params: options.params || {},
            meta: options.meta || {}
        };

        this.routes.set(path, route);
        console.log(`➕ تم تسجيل مسار: ${path}`);
    },

    /**
     * إضافة مستمعي الأحداث
     */
    addEventListeners: function() {
        // مراقبة تغيير الهاش
        window.addEventListener('hashchange', this.handleHashChange.bind(this));
        
        // مراقبة تحميل الصفحة
        window.addEventListener('load', this.handlePageLoad.bind(this));
        
        // مراقبة أزرار التنقل
        window.addEventListener('popstate', this.handlePopState.bind(this));
    },

    /**
     * معالج تغيير الهاش
     */
    handleHashChange: function(event) {
        const newHash = window.location.hash.substring(1);
        const oldHash = event.oldURL ? new URL(event.oldURL).hash.substring(1) : '';
        
        console.log(`🔄 تغيير المسار: ${oldHash} → ${newHash}`);
        
        this.navigate(newHash || this.config.defaultRoute, {}, false);
    },

    /**
     * معالج تحميل الصفحة
     */
    handlePageLoad: function() {
        this.loadCurrentRoute();
    },

    /**
     * معالج أزرار التنقل
     */
    handlePopState: function(event) {
        if (event.state && event.state.route) {
            this.navigate(event.state.route, event.state.params || {}, false);
        }
    },

    /**
     * تحميل المسار الحالي
     */
    loadCurrentRoute: function() {
        const hash = window.location.hash.substring(1);
        const route = hash || this.config.defaultRoute;
        
        this.navigate(route, {}, false);
    },

    /**
     * التنقل إلى مسار
     */
    navigate: function(path, params = {}, updateHistory = true) {
        if (this.state.isNavigating) {
            console.warn('⚠️ التنقل قيد التنفيذ بالفعل');
            return false;
        }

        try {
            this.state.isNavigating = true;

            // التحقق من وجود المسار
            const route = this.routes.get(path);
            if (!route) {
                console.warn(`⚠️ مسار غير موجود: ${path}`);
                this.navigate(this.config.defaultRoute, {}, updateHistory);
                return false;
            }

            // التحقق من المصادقة
            if (route.requiresAuth && !this.checkAuth()) {
                console.warn('⚠️ المسار يتطلب مصادقة');
                this.navigate('login', {}, updateHistory);
                return false;
            }

            // تنفيذ beforeEnter
            if (route.beforeEnter && typeof route.beforeEnter === 'function') {
                const canEnter = route.beforeEnter(route, params);
                if (canEnter === false) {
                    console.warn('⚠️ تم منع الدخول للمسار');
                    this.state.isNavigating = false;
                    return false;
                }
            }

            // تحديث الحالة
            this.currentRoute = route;
            this.currentParams = { ...route.params, ...params };

            // تحديث الرابط
            if (updateHistory) {
                this.updateURL(path, this.currentParams);
            }

            // تحديث العنوان
            this.updateTitle(route.title);

            // تحميل المكون
            this.loadComponent(route, this.currentParams);

            // إضافة للتاريخ
            if (updateHistory) {
                this.addToHistory(path, this.currentParams);
            }

            // تنفيذ afterEnter
            if (route.afterEnter && typeof route.afterEnter === 'function') {
                route.afterEnter(route, this.currentParams);
            }

            // التمرير للأعلى
            if (this.config.scrollToTop) {
                window.scrollTo(0, 0);
            }

            // إرسال حدث التنقل
            this.emitNavigationEvent(path, this.currentParams);

            console.log(`✅ تم التنقل إلى: ${path}`);
            return true;

        } catch (error) {
            console.error('❌ خطأ في التنقل:', error);
            return false;
        } finally {
            this.state.isNavigating = false;
        }
    },

    /**
     * تحديث الرابط
     */
    updateURL: function(path, params) {
        let url = `${this.config.hashPrefix}${path}`;
        
        // إضافة المعاملات
        const queryParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                queryParams.append(key, params[key]);
            }
        });
        
        if (queryParams.toString()) {
            url += `?${queryParams.toString()}`;
        }

        // تحديث التاريخ
        if (this.config.enableHistory) {
            const state = { route: path, params: params };
            history.pushState(state, '', url);
        } else {
            window.location.hash = url.substring(1);
        }
    },

    /**
     * تحديث عنوان الصفحة
     */
    updateTitle: function(title) {
        const appName = window.App?.config?.name || 'قمة الوعد للسفريات';
        document.title = `${title} - ${appName}`;
    },

    /**
     * تحميل المكون
     */
    loadComponent: function(route, params) {
        if (!route.component) {
            console.warn('⚠️ لا يوجد مكون محدد للمسار');
            return;
        }

        const component = window[route.component];
        if (!component) {
            console.error(`❌ المكون غير موجود: ${route.component}`);
            return;
        }

        // تحديث المحتوى
        if (window.App && window.App.elements.mainContent) {
            try {
                // عرض المكون
                const content = component.render ? component.render(params) : '';
                window.App.elements.mainContent.innerHTML = content;

                // تهيئة المكون
                if (component.init && typeof component.init === 'function') {
                    component.init(params);
                }

                console.log(`✅ تم تحميل المكون: ${route.component}`);
            } catch (error) {
                console.error(`❌ خطأ في تحميل المكون: ${route.component}`, error);
                this.showError('حدث خطأ في تحميل الصفحة');
            }
        }
    },

    /**
     * التحقق من المصادقة
     */
    checkAuth: function() {
        // يمكن تخصيص هذه الدالة حسب نظام المصادقة
        return window.Auth ? window.Auth.isAuthenticated() : true;
    },

    /**
     * إضافة للتاريخ
     */
    addToHistory: function(path, params) {
        this.history.push({
            path: path,
            params: params,
            timestamp: new Date()
        });

        // الاحتفاظ بآخر 50 مدخل فقط
        if (this.history.length > 50) {
            this.history = this.history.slice(-50);
        }
    },

    /**
     * إرسال حدث التنقل
     */
    emitNavigationEvent: function(path, params) {
        const event = new CustomEvent('router:navigate', {
            detail: {
                path: path,
                params: params,
                route: this.currentRoute
            }
        });
        
        document.dispatchEvent(event);
    },

    /**
     * عرض رسالة خطأ
     */
    showError: function(message) {
        if (window.Notifications) {
            window.Notifications.error(message);
        } else {
            console.error(message);
        }
    },

    /**
     * الحصول على المسار الحالي
     */
    getCurrentRoute: function() {
        return this.currentRoute;
    },

    /**
     * الحصول على معاملات المسار الحالي
     */
    getCurrentParams: function() {
        return this.currentParams;
    },

    /**
     * الحصول على تاريخ التنقل
     */
    getHistory: function() {
        return [...this.history];
    },

    /**
     * العودة للمسار السابق
     */
    goBack: function() {
        if (this.history.length > 1) {
            const previousRoute = this.history[this.history.length - 2];
            this.navigate(previousRoute.path, previousRoute.params);
        } else {
            this.navigate(this.config.defaultRoute);
        }
    },

    /**
     * إعادة تحميل المسار الحالي
     */
    reload: function() {
        if (this.currentRoute) {
            this.navigate(this.currentRoute.path, this.currentParams, false);
        }
    },

    /**
     * التحقق من كون المسار نشط
     */
    isActive: function(path) {
        return this.currentRoute && this.currentRoute.path === path;
    }
};

// تصدير نظام التوجيه للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Router;
}

# دليل نظام القوالب - قيمة الوعد
## Templates System Guide - Qimat Alwaed

### نظرة عامة
نظام إدارة القوالب هو نظام شامل ومتطور لإنشاء وإدارة قوالب المستندات المحاسبية لشركات السفريات. يوفر النظام واجهة سهلة الاستخدام مع محرر مرئي متقدم ونظام معاينة وطباعة احترافي.

### المكونات الرئيسية

#### 1. واجهة إدارة القوالب (Templates Management)
- **الملف**: `src/pages/templates-management.html`
- **الوظائف**:
  - عرض جميع القوالب المتاحة
  - تصنيف القوالب حسب النوع (سندات، تقارير، فواتير، حسابات)
  - البحث والتصفية المتقدم
  - إحصائيات شاملة
  - أدوات سريعة للإدارة

#### 2. محرر القوالب المرئي (Template Editor)
- **الملف**: `src/pages/template-editor.html`
- **الوظائف**:
  - تحرير مرئي للقوالب
  - لوحة خصائص متقدمة
  - أدوات التصميم والتنسيق
  - إضافة وتحرير العناصر
  - معاينة فورية
  - حفظ وتصدير

#### 3. نظام المعاينة والطباعة (Preview System)
- **الملف**: `src/pages/template-preview.html`
- **الوظائف**:
  - معاينة متقدمة مع تحكم في التكبير
  - أوضاع عرض متعددة
  - إعدادات طباعة شاملة
  - تصدير متعدد الصيغ (PDF, Word, صورة)
  - أدوات مساعدة (مساطر، شبكة، هوامش)

#### 4. القوالب الجاهزة (Ready Templates)
- **سند القبض**: `src/templates/receipt-voucher.html`
- **سند الدفع**: `src/templates/payment-voucher.html`
- **التقرير المالي**: `src/templates/financial-report.html`

### التقنيات المستخدمة

#### Frontend Technologies
- **Bootstrap 5.3.2**: إطار العمل للتصميم المتجاوب
- **Font Awesome 6.5**: مكتبة الأيقونات
- **Cairo Font**: خط عربي احترافي
- **CSS Grid & Flexbox**: تخطيط متقدم
- **CSS Custom Properties**: متغيرات CSS للثيمات
- **CSS Animations**: تأثيرات بصرية متقدمة

#### JavaScript Features
- **ES6+ Syntax**: استخدام أحدث معايير JavaScript
- **DOM Manipulation**: تلاعب متقدم بعناصر الصفحة
- **Event Handling**: إدارة الأحداث والتفاعل
- **LocalStorage**: حفظ البيانات محلياً
- **Drag & Drop API**: سحب وإفلات العناصر
- **Print API**: واجهة الطباعة المتقدمة

### هيكل الملفات

```
src/
├── pages/
│   ├── templates-management.html    # واجهة إدارة القوالب
│   ├── template-editor.html         # محرر القوالب
│   └── template-preview.html        # نظام المعاينة
├── css/
│   ├── templates-management.css     # تنسيق إدارة القوالب
│   ├── template-editor.css          # تنسيق المحرر
│   └── template-preview.css         # تنسيق المعاينة
├── js/
│   ├── templates-management.js      # وظائف إدارة القوالب
│   ├── template-editor.js           # وظائف المحرر
│   └── template-preview.js          # وظائف المعاينة
├── templates/
│   ├── receipt-voucher.html         # قالب سند القبض
│   ├── payment-voucher.html         # قالب سند الدفع
│   └── financial-report.html        # قالب التقرير المالي
├── tests/
│   └── templates-test.html          # نظام اختبار شامل
└── docs/
    └── templates-system-guide.md    # هذا الدليل
```

### دليل الاستخدام

#### 1. الوصول إلى نظام القوالب
1. من لوحة التحكم الرئيسية، انقر على "القوالب"
2. ستفتح واجهة إدارة القوالب الرئيسية

#### 2. إنشاء قالب جديد
1. في واجهة إدارة القوالب، انقر على "قالب جديد"
2. اختر نوع القالب المطلوب
3. سيفتح محرر القوالب المرئي
4. استخدم الأدوات المتاحة لتصميم القالب
5. احفظ القالب عند الانتهاء

#### 3. تحرير قالب موجود
1. في واجهة إدارة القوالب، ابحث عن القالب المطلوب
2. انقر على "تحرير"
3. سيفتح المحرر مع القالب المحدد
4. قم بالتعديلات المطلوبة
5. احفظ التغييرات

#### 4. معاينة وطباعة القالب
1. من المحرر، انقر على "معاينة متقدمة"
2. ستفتح نافذة المعاينة في تبويب جديد
3. استخدم أدوات التحكم للتكبير والتصغير
4. اضبط إعدادات الطباعة حسب الحاجة
5. انقر على "طباعة" أو "تصدير"

### الميزات المتقدمة

#### 1. محرر القوالب المرئي
- **تحديد العناصر**: انقر على أي عنصر لتحديده وتحريره
- **لوحة الخصائص**: تحرير خصائص العنصر المحدد
- **أدوات التصميم**: تغيير الألوان والخطوط والأحجام
- **إضافة عناصر**: إضافة نصوص وصور وجداول جديدة
- **اختصارات لوحة المفاتيح**: Ctrl+S للحفظ، Ctrl+Z للتراجع

#### 2. نظام المعاينة المتقدم
- **أوضاع العرض**: ملء العرض، ملء الصفحة، الحجم الفعلي
- **التكبير والتصغير**: من 50% إلى 200%
- **أدوات مساعدة**: مساطر، شبكة، عرض الهوامش
- **خلفيات متعددة**: أبيض، رمادي، داكن
- **ملء الشاشة**: وضع ملء الشاشة للمعاينة

#### 3. إعدادات الطباعة الشاملة
- **أحجام الورق**: A4, A3, Letter, Legal
- **الاتجاه**: عمودي أو أفقي
- **الهوامش**: عادي، ضيق، واسع
- **طباعة الخلفيات**: تفعيل أو إلغاء طباعة الألوان والخلفيات

### نصائح للاستخدام الأمثل

#### 1. تصميم القوالب
- استخدم ألوان متناسقة مع هوية الشركة
- اجعل النصوص واضحة وسهلة القراءة
- اترك مساحات كافية بين العناصر
- تأكد من أن القالب يبدو جيداً عند الطباعة

#### 2. تنظيم القوالب
- استخدم أسماء واضحة ووصفية للقوالب
- صنف القوالب حسب النوع والاستخدام
- احتفظ بنسخ احتياطية من القوالب المهمة
- راجع وحدث القوالب بانتظام

#### 3. الأداء والسرعة
- تجنب استخدام صور كبيرة الحجم
- استخدم الألوان بحكمة لتوفير الحبر
- اختبر القوالب على أجهزة مختلفة
- تأكد من سرعة تحميل القوالب

### استكشاف الأخطاء وإصلاحها

#### مشاكل شائعة وحلولها

**1. القالب لا يظهر بشكل صحيح**
- تأكد من أن المتصفح يدعم CSS الحديث
- امسح ذاكرة التخزين المؤقت للمتصفح
- تحقق من اتصال الإنترنت

**2. مشاكل في الطباعة**
- تأكد من إعدادات الطابعة
- جرب أحجام ورق مختلفة
- تحقق من إعدادات الهوامش

**3. بطء في التحميل**
- تحقق من سرعة الإنترنت
- قلل من حجم الصور المستخدمة
- أغلق التطبيقات الأخرى

### التطوير والتحديث

#### إضافة قوالب جديدة
1. أنشئ ملف HTML جديد في مجلد `templates/`
2. استخدم نفس هيكل القوالب الموجودة
3. أضف التنسيق المناسب
4. اختبر القالب في جميع المتصفحات

#### تخصيص التصميم
1. عدل ملفات CSS في مجلد `css/`
2. استخدم متغيرات CSS للألوان والخطوط
3. تأكد من التوافق مع الأجهزة المختلفة
4. اختبر التغييرات بعناية

### الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- استخدم نظام الاختبار المدمج
- تواصل مع فريق التطوير
- ارجع إلى الوثائق التقنية

### معلومات إضافية

**تاريخ الإنشاء**: 2024
**الإصدار**: 1.0
**التوافق**: جميع المتصفحات الحديثة
**اللغة**: العربية (RTL)
**الترخيص**: خاص بشركة قيمة الوعد

---

*هذا الدليل يغطي جميع جوانب نظام القوالب. للحصول على معلومات تقنية أكثر تفصيلاً، راجع التعليقات في ملفات الكود المصدري.*

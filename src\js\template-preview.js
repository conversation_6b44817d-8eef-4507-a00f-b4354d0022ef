/**
 * نظام معاينة القوالب المتقدم
 * Template Preview System
 */

// ===== المتغيرات العامة =====
let currentZoom = 100;
let isFullscreen = false;
let currentTemplate = null;

// ===== تهيئة النظام =====
document.addEventListener('DOMContentLoaded', function() {
    initializePreview();
    setupEventListeners();
    loadTemplateFromURL();
    updateScrollProgress();
});

/**
 * تهيئة نظام المعاينة
 */
function initializePreview() {
    // تحديث شريط التقدم عند التمرير
    window.addEventListener('scroll', updateScrollProgress);
    
    // تحديث حجم النافذة
    window.addEventListener('resize', handleResize);
    
    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts();
    
    console.log('تم تهيئة نظام المعاينة بنجاح');
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // أحداث التكبير والتصغير
    document.getElementById('zoomSlider').addEventListener('input', function() {
        setZoom(this.value);
    });
    
    // منع النقر بالزر الأيمن على المحتوى
    document.getElementById('templateContent').addEventListener('contextmenu', function(e) {
        e.preventDefault();
        showContextMenu(e);
    });
}

/**
 * تحميل القالب من URL
 */
function loadTemplateFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const templateId = urlParams.get('template');
    
    if (templateId) {
        loadTemplate(templateId);
    }
}

/**
 * تحميل قالب محدد
 */
function loadTemplate(templateId) {
    // محاكاة تحميل القالب
    showNotification(`جاري تحميل القالب: ${templateId}`, 'info');
    
    // هنا يمكن إضافة منطق تحميل القالب الفعلي
    setTimeout(() => {
        currentTemplate = templateId;
        showNotification('تم تحميل القالب بنجاح', 'success');
    }, 1000);
}

// ===== وظائف التكبير والتصغير =====

/**
 * تعيين مستوى التكبير
 */
function setZoom(value) {
    currentZoom = parseInt(value);
    const previewPage = document.getElementById('previewPage');
    const zoomValue = document.getElementById('zoomValue');
    
    previewPage.style.transform = `scale(${currentZoom / 100})`;
    previewPage.style.transformOrigin = 'top center';
    zoomValue.textContent = `${currentZoom}%`;
    
    // تحديث شريط التمرير
    document.getElementById('zoomSlider').value = currentZoom;
}

/**
 * تكبير العرض
 */
function zoomIn() {
    if (currentZoom < 200) {
        setZoom(currentZoom + 10);
    }
}

/**
 * تصغير العرض
 */
function zoomOut() {
    if (currentZoom > 50) {
        setZoom(currentZoom - 10);
    }
}

/**
 * تغيير وضع العرض
 */
function changeViewMode(mode) {
    const previewArea = document.getElementById('previewArea');
    
    // إزالة الفئات السابقة
    previewArea.classList.remove('fit-width', 'fit-page', 'actual-size');
    
    // إضافة الفئة الجديدة
    previewArea.classList.add(mode);
    
    // تحديث التكبير حسب الوضع
    switch(mode) {
        case 'fit-width':
            setZoom(80);
            break;
        case 'fit-page':
            setZoom(60);
            break;
        case 'actual-size':
            setZoom(100);
            break;
    }
    
    showNotification(`تم تغيير وضع العرض إلى: ${getViewModeText(mode)}`, 'success');
}

/**
 * الحصول على نص وضع العرض
 */
function getViewModeText(mode) {
    const modes = {
        'fit-width': 'ملء العرض',
        'fit-page': 'ملء الصفحة',
        'actual-size': 'الحجم الفعلي'
    };
    return modes[mode] || mode;
}

/**
 * تغيير خلفية العرض
 */
function changeBackground(background) {
    const previewArea = document.getElementById('previewArea');
    
    // إزالة فئات الخلفية السابقة
    previewArea.classList.remove('bg-white', 'bg-gray', 'bg-dark');
    
    // إضافة الفئة الجديدة
    if (background !== 'white') {
        previewArea.classList.add(`bg-${background}`);
    }
    
    showNotification(`تم تغيير خلفية العرض`, 'success');
}

// ===== وظائف العرض المساعدة =====

/**
 * تبديل المساطر
 */
function toggleRulers() {
    const previewArea = document.getElementById('previewArea');
    const showRulers = document.getElementById('showRulers').checked;
    
    if (showRulers) {
        previewArea.classList.add('show-rulers');
        showNotification('تم إظهار المساطر', 'info');
    } else {
        previewArea.classList.remove('show-rulers');
        showNotification('تم إخفاء المساطر', 'info');
    }
}

/**
 * تبديل الشبكة
 */
function toggleGrid() {
    const previewArea = document.getElementById('previewArea');
    const showGrid = document.getElementById('showGrid').checked;
    
    if (showGrid) {
        previewArea.classList.add('show-grid');
        showNotification('تم إظهار الشبكة', 'info');
    } else {
        previewArea.classList.remove('show-grid');
        showNotification('تم إخفاء الشبكة', 'info');
    }
}

/**
 * تبديل الهوامش
 */
function toggleMargins() {
    const previewArea = document.getElementById('previewArea');
    const showMargins = document.getElementById('showMargins').checked;
    
    if (showMargins) {
        previewArea.classList.add('show-margins');
        showNotification('تم إظهار الهوامش', 'info');
    } else {
        previewArea.classList.remove('show-margins');
        showNotification('تم إخفاء الهوامش', 'info');
    }
}

// ===== وظائف ملء الشاشة =====

/**
 * تبديل ملء الشاشة
 */
function toggleFullscreen() {
    if (!isFullscreen) {
        enterFullscreen();
    } else {
        exitFullscreen();
    }
}

/**
 * دخول وضع ملء الشاشة
 */
function enterFullscreen() {
    const element = document.documentElement;
    
    if (element.requestFullscreen) {
        element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }
    
    isFullscreen = true;
    showNotification('تم تفعيل وضع ملء الشاشة', 'success');
}

/**
 * الخروج من وضع ملء الشاشة
 */
function exitFullscreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }
    
    isFullscreen = false;
    showNotification('تم إلغاء وضع ملء الشاشة', 'info');
}

// ===== وظائف الطباعة =====

/**
 * طباعة القالب
 */
function printTemplate() {
    // إظهار نافذة إعدادات الطباعة
    const printModal = new bootstrap.Modal(document.getElementById('printSettingsModal'));
    printModal.show();
}

/**
 * تأكيد الطباعة
 */
function confirmPrint() {
    const paperSize = document.getElementById('paperSize').value;
    const orientation = document.getElementById('orientation').value;
    const margins = document.getElementById('margins').value;
    const printBackground = document.getElementById('printBackground').checked;
    
    // إعداد خصائص الطباعة
    const printCSS = generatePrintCSS(paperSize, orientation, margins, printBackground);
    
    // إضافة CSS للطباعة
    const style = document.createElement('style');
    style.textContent = printCSS;
    document.head.appendChild(style);
    
    // طباعة الصفحة
    window.print();
    
    // إزالة CSS بعد الطباعة
    setTimeout(() => {
        document.head.removeChild(style);
    }, 1000);
    
    // إغلاق النافذة
    const printModal = bootstrap.Modal.getInstance(document.getElementById('printSettingsModal'));
    printModal.hide();
    
    showNotification('تم إرسال القالب للطباعة', 'success');
}

/**
 * إنشاء CSS للطباعة
 */
function generatePrintCSS(paperSize, orientation, margins, printBackground) {
    let css = `
        @media print {
            @page {
                size: ${paperSize} ${orientation};
                margin: ${getPrintMargins(margins)};
            }
    `;
    
    if (printBackground) {
        css += `
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
        `;
    }
    
    css += `}`;
    return css;
}

/**
 * الحصول على هوامش الطباعة
 */
function getPrintMargins(margins) {
    const marginValues = {
        'normal': '2.54cm',
        'narrow': '1.27cm',
        'wide': '3.81cm'
    };
    return marginValues[margins] || '2.54cm';
}

// ===== وظائف التصدير =====

/**
 * تصدير إلى PDF
 */
function exportToPDF() {
    showNotification('جاري تصدير القالب إلى PDF...', 'info');
    
    // هنا يمكن إضافة منطق التصدير الفعلي
    setTimeout(() => {
        showNotification('تم تصدير القالب إلى PDF بنجاح', 'success');
    }, 2000);
}

/**
 * تصدير إلى Word
 */
function exportToWord() {
    showNotification('جاري تصدير القالب إلى Word...', 'info');
    
    // هنا يمكن إضافة منطق التصدير الفعلي
    setTimeout(() => {
        showNotification('تم تصدير القالب إلى Word بنجاح', 'success');
    }, 2000);
}

/**
 * تصدير إلى صورة
 */
function exportToImage() {
    showNotification('جاري تصدير القالب إلى صورة...', 'info');
    
    // هنا يمكن إضافة منطق التصدير الفعلي
    setTimeout(() => {
        showNotification('تم تصدير القالب إلى صورة بنجاح', 'success');
    }, 2000);
}

// ===== وظائف مساعدة =====

/**
 * تحديث شريط التقدم
 */
function updateScrollProgress() {
    const scrollProgress = document.getElementById('scrollProgress');
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / scrollHeight) * 100;
    
    scrollProgress.style.width = `${Math.min(progress, 100)}%`;
}

/**
 * التعامل مع تغيير حجم النافذة
 */
function handleResize() {
    // إعادة حساب التكبير للأوضاع التلقائية
    const previewArea = document.getElementById('previewArea');
    
    if (previewArea.classList.contains('fit-width')) {
        changeViewMode('fit-width');
    } else if (previewArea.classList.contains('fit-page')) {
        changeViewMode('fit-page');
    }
}

/**
 * إعداد اختصارات لوحة المفاتيح
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + P للطباعة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printTemplate();
        }
        
        // F11 لملء الشاشة
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
        
        // + للتكبير
        if (e.key === '+' || e.key === '=') {
            e.preventDefault();
            zoomIn();
        }
        
        // - للتصغير
        if (e.key === '-') {
            e.preventDefault();
            zoomOut();
        }
        
        // 0 للحجم الطبيعي
        if (e.key === '0') {
            e.preventDefault();
            setZoom(100);
        }
    });
}

/**
 * إظهار قائمة السياق
 */
function showContextMenu(e) {
    // يمكن إضافة قائمة سياق مخصصة هنا
    console.log('قائمة السياق في الموضع:', e.clientX, e.clientY);
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود
    if (window.showAdvancedNotification) {
        window.showAdvancedNotification(message, type);
    } else {
        // إنشاء إشعار بسيط
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 100px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

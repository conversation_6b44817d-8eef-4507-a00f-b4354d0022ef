<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة القوالب المحاسبية - نظام قيمة الوعد</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../styles/main.css">
    <link rel="stylesheet" href="../css/accounting-enhanced.css">
    <link rel="stylesheet" href="../css/accounting-interface.css">
    <link rel="stylesheet" href="../css/interactive-enhancements.css">
    <link rel="stylesheet" href="../css/templates-management.css">
</head>
<body>
    <!-- شريط التقدم للتمرير -->
    <div class="scroll-progress" id="scrollProgress"></div>
    
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../index.html">
                <i class="fas fa-plane me-2"></i>
                قيمة الوعد
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../../index.html">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="accounting-dashboard.html">
                            <i class="fas fa-calculator"></i> النظام المحاسبي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="templates-management.html">
                            <i class="fas fa-file-alt"></i> إدارة القوالب
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> المستخدم
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog"></i> الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="templates-dashboard">
        <div class="templates-container">
            <!-- رأس النظام -->
            <div class="templates-header fade-in-up">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="header-text">
                        <h1>إدارة القوالب المحاسبية</h1>
                        <p>تصميم وتخصيص قوالب السندات والحسابات والتقارير المالية</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="createNewTemplate()">
                        <i class="fas fa-plus me-2"></i>قالب جديد
                    </button>
                    <button class="btn btn-outline-primary" onclick="openTemplateEditor()">
                        <i class="fas fa-edit me-2"></i>محرر القوالب
                    </button>
                    <button class="btn btn-outline-secondary" onclick="importTemplate()">
                        <i class="fas fa-upload me-2"></i>استيراد قالب
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="templates-stats-grid fade-in-up" style="animation-delay: 0.1s;">
                <div class="stat-card stat-primary">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number counting-animation">12</h3>
                        <p class="stat-label">قوالب السندات</p>
                    </div>
                </div>
                
                <div class="stat-card stat-success">
                    <div class="stat-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number counting-animation">8</h3>
                        <p class="stat-label">قوالب التقارير</p>
                    </div>
                </div>
                
                <div class="stat-card stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number counting-animation">5</h3>
                        <p class="stat-label">قوالب مخصصة</p>
                    </div>
                </div>
                
                <div class="stat-card stat-warning">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number counting-animation">156</h3>
                        <p class="stat-label">مرة استخدام</p>
                    </div>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="templates-filters fade-in-up" style="animation-delay: 0.2s;">
                <div class="filters-row">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="templateSearch" placeholder="البحث في القوالب..." onkeyup="filterTemplates()">
                    </div>
                    
                    <div class="filter-group">
                        <select id="categoryFilter" onchange="filterTemplates()">
                            <option value="">جميع الفئات</option>
                            <option value="vouchers">السندات</option>
                            <option value="reports">التقارير</option>
                            <option value="invoices">الفواتير</option>
                            <option value="accounts">الحسابات</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select id="statusFilter" onchange="filterTemplates()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="draft">مسودة</option>
                            <option value="archived">مؤرشف</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>مسح الفلاتر
                    </button>
                </div>
            </div>

            <!-- فئات القوالب -->
            <div class="templates-categories fade-in-up" style="animation-delay: 0.3s;">
                <div class="category-tabs">
                    <button class="category-tab active" data-category="all" onclick="switchCategory('all')">
                        <i class="fas fa-th-large me-2"></i>جميع القوالب
                    </button>
                    <button class="category-tab" data-category="vouchers" onclick="switchCategory('vouchers')">
                        <i class="fas fa-file-invoice me-2"></i>السندات المحاسبية
                    </button>
                    <button class="category-tab" data-category="reports" onclick="switchCategory('reports')">
                        <i class="fas fa-chart-line me-2"></i>التقارير المالية
                    </button>
                    <button class="category-tab" data-category="invoices" onclick="switchCategory('invoices')">
                        <i class="fas fa-receipt me-2"></i>الفواتير والإيصالات
                    </button>
                    <button class="category-tab" data-category="accounts" onclick="switchCategory('accounts')">
                        <i class="fas fa-book me-2"></i>كشوف الحسابات
                    </button>
                </div>
            </div>

            <!-- شبكة القوالب -->
            <div class="templates-grid fade-in-up" style="animation-delay: 0.4s;" id="templatesGrid">
                <!-- سيتم ملء القوالب هنا بواسطة JavaScript -->
            </div>

            <!-- أدوات سريعة -->
            <div class="quick-tools fade-in-up" style="animation-delay: 0.5s;">
                <h3><i class="fas fa-tools me-2"></i>أدوات سريعة</h3>
                <div class="tools-grid">
                    <div class="tool-card" onclick="openTemplateWizard()">
                        <i class="fas fa-magic"></i>
                        <h4>معالج القوالب</h4>
                        <p>إنشاء قالب جديد بخطوات مبسطة</p>
                    </div>
                    
                    <div class="tool-card" onclick="openBulkEditor()">
                        <i class="fas fa-edit"></i>
                        <h4>التحرير المجمع</h4>
                        <p>تعديل عدة قوالب في نفس الوقت</p>
                    </div>
                    
                    <div class="tool-card" onclick="exportTemplates()">
                        <i class="fas fa-download"></i>
                        <h4>تصدير القوالب</h4>
                        <p>تصدير القوالب للنسخ الاحتياطي</p>
                    </div>
                    
                    <div class="tool-card" onclick="openTemplateLibrary()">
                        <i class="fas fa-store"></i>
                        <h4>مكتبة القوالب</h4>
                        <p>قوالب جاهزة للاستخدام</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../js/accounting-dashboard.js"></script>
    <script src="../js/templates-management.js"></script>
</body>
</html>

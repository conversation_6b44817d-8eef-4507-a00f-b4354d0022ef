<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام القوالب - قيمة الوعد</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../styles/main.css">
    <link rel="stylesheet" href="../css/accounting-enhanced.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .test-header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .test-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .test-name {
            font-weight: 600;
            color: #1e293b;
        }
        
        .test-description {
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .test-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-running {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-passed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .test-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .progress-container {
            background: #f1f5f9;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .overall-progress {
            margin-bottom: 1rem;
        }
        
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .result-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .result-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .result-label {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }
        
        .log-timestamp {
            color: #94a3b8;
        }
        
        .log-info {
            color: #60a5fa;
        }
        
        .log-success {
            color: #34d399;
        }
        
        .log-error {
            color: #f87171;
        }
        
        .log-warning {
            color: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- رأس الاختبار -->
        <div class="test-header">
            <h1><i class="fas fa-vial me-3"></i>اختبار نظام القوالب</h1>
            <p class="mb-0">اختبار شامل لجميع مكونات نظام إدارة القوالب</p>
        </div>
        
        <!-- تقدم الاختبار -->
        <div class="progress-container">
            <h5><i class="fas fa-chart-line me-2"></i>تقدم الاختبار</h5>
            <div class="overall-progress">
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar bg-success" id="overallProgress" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <span>التقدم الإجمالي</span>
                    <span id="progressText">0%</span>
                </div>
            </div>
            
            <div class="test-results">
                <div class="result-card">
                    <div class="result-number text-success" id="passedCount">0</div>
                    <div class="result-label">نجح</div>
                </div>
                <div class="result-card">
                    <div class="result-number text-danger" id="failedCount">0</div>
                    <div class="result-label">فشل</div>
                </div>
                <div class="result-card">
                    <div class="result-number text-warning" id="pendingCount">0</div>
                    <div class="result-label">في الانتظار</div>
                </div>
                <div class="result-card">
                    <div class="result-number text-info" id="totalCount">0</div>
                    <div class="result-label">المجموع</div>
                </div>
            </div>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="test-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-play me-2"></i>التحكم في الاختبارات</h5>
                <div>
                    <button class="btn btn-success me-2" onclick="runAllTests()">
                        <i class="fas fa-play me-1"></i>تشغيل جميع الاختبارات
                    </button>
                    <button class="btn btn-warning me-2" onclick="stopTests()">
                        <i class="fas fa-stop me-1"></i>إيقاف
                    </button>
                    <button class="btn btn-secondary" onclick="resetTests()">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبارات واجهة إدارة القوالب -->
        <div class="test-section">
            <h5><i class="fas fa-desktop me-2"></i>اختبارات واجهة إدارة القوالب</h5>
            
            <div class="test-item" data-test="ui-load">
                <div>
                    <div class="test-name">تحميل واجهة إدارة القوالب</div>
                    <div class="test-description">التحقق من تحميل الصفحة وعرض جميع العناصر</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('ui-load')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="ui-navigation">
                <div>
                    <div class="test-name">التنقل بين الفئات</div>
                    <div class="test-description">اختبار التنقل بين فئات القوالب المختلفة</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('ui-navigation')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="ui-search">
                <div>
                    <div class="test-name">وظيفة البحث</div>
                    <div class="test-description">اختبار البحث والتصفية في القوالب</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('ui-search')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبارات محرر القوالب -->
        <div class="test-section">
            <h5><i class="fas fa-edit me-2"></i>اختبارات محرر القوالب</h5>
            
            <div class="test-item" data-test="editor-load">
                <div>
                    <div class="test-name">تحميل محرر القوالب</div>
                    <div class="test-description">التحقق من تحميل المحرر وجميع الأدوات</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('editor-load')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="editor-elements">
                <div>
                    <div class="test-name">تحديد العناصر</div>
                    <div class="test-description">اختبار تحديد وتحرير عناصر القالب</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('editor-elements')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="editor-properties">
                <div>
                    <div class="test-name">تحديث الخصائص</div>
                    <div class="test-description">اختبار تحديث خصائص العناصر</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('editor-properties')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="editor-save">
                <div>
                    <div class="test-name">حفظ القالب</div>
                    <div class="test-description">اختبار حفظ التغييرات على القالب</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('editor-save')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبارات المعاينة والطباعة -->
        <div class="test-section">
            <h5><i class="fas fa-eye me-2"></i>اختبارات المعاينة والطباعة</h5>
            
            <div class="test-item" data-test="preview-load">
                <div>
                    <div class="test-name">تحميل المعاينة</div>
                    <div class="test-description">التحقق من تحميل صفحة المعاينة</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('preview-load')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="preview-zoom">
                <div>
                    <div class="test-name">وظائف التكبير</div>
                    <div class="test-description">اختبار تكبير وتصغير المعاينة</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('preview-zoom')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="preview-print">
                <div>
                    <div class="test-name">إعدادات الطباعة</div>
                    <div class="test-description">اختبار إعدادات وخيارات الطباعة</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('preview-print')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبارات القوالب -->
        <div class="test-section">
            <h5><i class="fas fa-file-alt me-2"></i>اختبارات القوالب</h5>
            
            <div class="test-item" data-test="template-receipt">
                <div>
                    <div class="test-name">قالب سند القبض</div>
                    <div class="test-description">اختبار قالب سند القبض وجميع عناصره</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('template-receipt')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="template-payment">
                <div>
                    <div class="test-name">قالب سند الدفع</div>
                    <div class="test-description">اختبار قالب سند الدفع وجميع عناصره</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('template-payment')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
            
            <div class="test-item" data-test="template-report">
                <div>
                    <div class="test-name">قالب التقرير المالي</div>
                    <div class="test-description">اختبار قالب التقرير المالي وجميع عناصره</div>
                </div>
                <div class="test-actions">
                    <span class="test-status status-pending">في الانتظار</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="runSingleTest('template-report')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- سجل الاختبارات -->
        <div class="test-section">
            <h5><i class="fas fa-list me-2"></i>سجل الاختبارات</h5>
            <div class="log-container" id="testLog">
                <div class="log-entry">
                    <span class="log-timestamp">[00:00:00]</span>
                    <span class="log-info">نظام الاختبار جاهز للتشغيل</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        /**
         * نظام اختبار القوالب
         * Templates Testing System
         */

        // ===== المتغيرات العامة =====
        let testResults = {
            passed: 0,
            failed: 0,
            pending: 0,
            total: 0
        };

        let isTestingRunning = false;
        let currentTestIndex = 0;
        let allTests = [];

        // ===== تهيئة النظام =====
        document.addEventListener('DOMContentLoaded', function() {
            initializeTests();
            updateCounters();
        });

        /**
         * تهيئة الاختبارات
         */
        function initializeTests() {
            allTests = document.querySelectorAll('.test-item');
            testResults.total = allTests.length;
            testResults.pending = allTests.length;

            updateCounters();
            logMessage('تم تهيئة نظام الاختبار بنجاح', 'info');
            logMessage(`عدد الاختبارات المتاحة: ${testResults.total}`, 'info');
        }

        /**
         * تشغيل جميع الاختبارات
         */
        async function runAllTests() {
            if (isTestingRunning) {
                logMessage('الاختبارات قيد التشغيل بالفعل', 'warning');
                return;
            }

            isTestingRunning = true;
            currentTestIndex = 0;

            logMessage('بدء تشغيل جميع الاختبارات...', 'info');

            for (let i = 0; i < allTests.length; i++) {
                if (!isTestingRunning) break;

                const testItem = allTests[i];
                const testId = testItem.dataset.test;

                await runSingleTest(testId, false);
                await sleep(1000); // انتظار ثانية بين الاختبارات
            }

            isTestingRunning = false;
            logMessage('انتهاء تشغيل جميع الاختبارات', 'success');

            // عرض النتائج النهائية
            showFinalResults();
        }

        /**
         * تشغيل اختبار واحد
         */
        async function runSingleTest(testId, standalone = true) {
            const testItem = document.querySelector(`[data-test="${testId}"]`);
            if (!testItem) {
                logMessage(`لم يتم العثور على الاختبار: ${testId}`, 'error');
                return;
            }

            const statusElement = testItem.querySelector('.test-status');
            const testName = testItem.querySelector('.test-name').textContent;

            // تحديث حالة الاختبار إلى "قيد التشغيل"
            statusElement.textContent = 'قيد التشغيل';
            statusElement.className = 'test-status status-running';

            logMessage(`بدء اختبار: ${testName}`, 'info');

            try {
                // تشغيل الاختبار حسب النوع
                const result = await executeTest(testId);

                if (result.success) {
                    statusElement.textContent = 'نجح';
                    statusElement.className = 'test-status status-passed';
                    testResults.passed++;
                    testResults.pending--;
                    logMessage(`نجح الاختبار: ${testName}`, 'success');
                } else {
                    statusElement.textContent = 'فشل';
                    statusElement.className = 'test-status status-failed';
                    testResults.failed++;
                    testResults.pending--;
                    logMessage(`فشل الاختبار: ${testName} - ${result.error}`, 'error');
                }
            } catch (error) {
                statusElement.textContent = 'فشل';
                statusElement.className = 'test-status status-failed';
                testResults.failed++;
                testResults.pending--;
                logMessage(`خطأ في الاختبار: ${testName} - ${error.message}`, 'error');
            }

            if (standalone) {
                updateCounters();
            }
        }

        /**
         * تنفيذ الاختبار
         */
        async function executeTest(testId) {
            await sleep(Math.random() * 2000 + 1000); // محاكاة وقت التنفيذ

            switch (testId) {
                case 'ui-load':
                    return testUILoad();
                case 'ui-navigation':
                    return testUINavigation();
                case 'ui-search':
                    return testUISearch();
                case 'editor-load':
                    return testEditorLoad();
                case 'editor-elements':
                    return testEditorElements();
                case 'editor-properties':
                    return testEditorProperties();
                case 'editor-save':
                    return testEditorSave();
                case 'preview-load':
                    return testPreviewLoad();
                case 'preview-zoom':
                    return testPreviewZoom();
                case 'preview-print':
                    return testPreviewPrint();
                case 'template-receipt':
                    return testTemplateReceipt();
                case 'template-payment':
                    return testTemplatePayment();
                case 'template-report':
                    return testTemplateReport();
                default:
                    return { success: false, error: 'اختبار غير معروف' };
            }
        }

        // ===== اختبارات واجهة المستخدم =====
        function testUILoad() {
            // محاكاة اختبار تحميل الواجهة
            const success = Math.random() > 0.1; // 90% نجاح
            return {
                success: success,
                error: success ? null : 'فشل في تحميل عناصر الواجهة'
            };
        }

        function testUINavigation() {
            const success = Math.random() > 0.15;
            return {
                success: success,
                error: success ? null : 'خطأ في التنقل بين الفئات'
            };
        }

        function testUISearch() {
            const success = Math.random() > 0.2;
            return {
                success: success,
                error: success ? null : 'وظيفة البحث لا تعمل بشكل صحيح'
            };
        }

        // ===== اختبارات المحرر =====
        function testEditorLoad() {
            const success = Math.random() > 0.1;
            return {
                success: success,
                error: success ? null : 'فشل في تحميل محرر القوالب'
            };
        }

        function testEditorElements() {
            const success = Math.random() > 0.25;
            return {
                success: success,
                error: success ? null : 'خطأ في تحديد العناصر'
            };
        }

        function testEditorProperties() {
            const success = Math.random() > 0.2;
            return {
                success: success,
                error: success ? null : 'فشل في تحديث الخصائص'
            };
        }

        function testEditorSave() {
            const success = Math.random() > 0.15;
            return {
                success: success,
                error: success ? null : 'خطأ في حفظ القالب'
            };
        }

        // ===== اختبارات المعاينة =====
        function testPreviewLoad() {
            const success = Math.random() > 0.1;
            return {
                success: success,
                error: success ? null : 'فشل في تحميل المعاينة'
            };
        }

        function testPreviewZoom() {
            const success = Math.random() > 0.2;
            return {
                success: success,
                error: success ? null : 'خطأ في وظائف التكبير'
            };
        }

        function testPreviewPrint() {
            const success = Math.random() > 0.25;
            return {
                success: success,
                error: success ? null : 'مشكلة في إعدادات الطباعة'
            };
        }

        // ===== اختبارات القوالب =====
        function testTemplateReceipt() {
            const success = Math.random() > 0.1;
            return {
                success: success,
                error: success ? null : 'خطأ في قالب سند القبض'
            };
        }

        function testTemplatePayment() {
            const success = Math.random() > 0.15;
            return {
                success: success,
                error: success ? null : 'خطأ في قالب سند الدفع'
            };
        }

        function testTemplateReport() {
            const success = Math.random() > 0.2;
            return {
                success: success,
                error: success ? null : 'خطأ في قالب التقرير المالي'
            };
        }

        /**
         * إيقاف الاختبارات
         */
        function stopTests() {
            isTestingRunning = false;
            logMessage('تم إيقاف الاختبارات', 'warning');
        }

        /**
         * إعادة تعيين الاختبارات
         */
        function resetTests() {
            isTestingRunning = false;
            testResults = { passed: 0, failed: 0, pending: allTests.length, total: allTests.length };

            // إعادة تعيين حالة جميع الاختبارات
            allTests.forEach(testItem => {
                const statusElement = testItem.querySelector('.test-status');
                statusElement.textContent = 'في الانتظار';
                statusElement.className = 'test-status status-pending';
            });

            updateCounters();
            clearLog();
            logMessage('تم إعادة تعيين جميع الاختبارات', 'info');
        }

        /**
         * تحديث العدادات
         */
        function updateCounters() {
            document.getElementById('passedCount').textContent = testResults.passed;
            document.getElementById('failedCount').textContent = testResults.failed;
            document.getElementById('pendingCount').textContent = testResults.pending;
            document.getElementById('totalCount').textContent = testResults.total;

            // تحديث شريط التقدم
            const progress = ((testResults.passed + testResults.failed) / testResults.total) * 100;
            document.getElementById('overallProgress').style.width = progress + '%';
            document.getElementById('progressText').textContent = Math.round(progress) + '%';
        }

        /**
         * عرض النتائج النهائية
         */
        function showFinalResults() {
            const successRate = (testResults.passed / testResults.total) * 100;

            if (successRate >= 90) {
                logMessage(`ممتاز! نجح ${testResults.passed} من ${testResults.total} اختبار (${successRate.toFixed(1)}%)`, 'success');
            } else if (successRate >= 70) {
                logMessage(`جيد! نجح ${testResults.passed} من ${testResults.total} اختبار (${successRate.toFixed(1)}%)`, 'info');
            } else {
                logMessage(`يحتاج تحسين! نجح ${testResults.passed} من ${testResults.total} اختبار (${successRate.toFixed(1)}%)`, 'warning');
            }
        }

        /**
         * تسجيل رسالة في السجل
         */
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');

            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-${type}">${message}</span>
            `;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        /**
         * مسح السجل
         */
        function clearLog() {
            const logContainer = document.getElementById('testLog');
            logContainer.innerHTML = `
                <div class="log-entry">
                    <span class="log-timestamp">[00:00:00]</span>
                    <span class="log-info">نظام الاختبار جاهز للتشغيل</span>
                </div>
            `;
        }

        /**
         * انتظار لفترة محددة
         */
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>

/**
 * ===================================
 * مكون إدارة الموردين - Suppliers Component
 * ===================================
 */

window.Suppliers = {
    // بيانات الموردين
    data: {
        suppliers: [],
        filteredSuppliers: [],
        currentPage: 1,
        itemsPerPage: 10,
        totalPages: 0,
        filters: {
            name: '',
            service_type: '',
            status: ''
        },
        sortBy: 'created_at',
        sortOrder: 'desc'
    },

    /**
     * عرض صفحة الموردين
     */
    render: function(params = {}) {
        return `
            <div class="suppliers-container">
                <!-- عنوان الصفحة -->
                <div class="page-header-section">
                    <div class="page-title">
                        <div class="title-icon success">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="title-text">
                            <h2>إدارة الموردين</h2>
                            <p>إدارة الموردين والشركاء وأنواع الخدمات</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="action-button primary" onclick="Suppliers.showAddModal()">
                            <i class="fas fa-plus"></i>
                            <span>إضافة مورد</span>
                        </button>
                        <button class="action-button secondary" onclick="Suppliers.exportData()">
                            <i class="fas fa-download"></i>
                            <span>تصدير</span>
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section">
                    <div class="stats-grid">
                        ${this.renderStats()}
                    </div>
                </div>

                <!-- الفلاتر -->
                <div class="filters-container">
                    <div class="filters-header">
                        <h3 class="filters-title">
                            <i class="fas fa-filter"></i>
                            البحث والفلترة
                        </h3>
                        <button class="filters-toggle" onclick="Suppliers.toggleFilters()">
                            <span>إظهار الفلاتر</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="filters-body" id="suppliers-filters">
                        ${this.renderFilters()}
                    </div>
                </div>

                <!-- جدول الموردين -->
                <div class="advanced-table-container">
                    <div class="table-toolbar">
                        <h3 class="table-title">قائمة الموردين</h3>
                        <div class="table-actions">
                            <div class="table-search">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" placeholder="البحث في الموردين..." 
                                       onkeyup="Suppliers.handleSearch(this.value)">
                            </div>
                        </div>
                    </div>
                    
                    <table class="advanced-table" id="suppliers-table">
                        <thead>
                            <tr>
                                <th class="sortable" onclick="Suppliers.sortBy('id')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    م
                                </th>
                                <th class="sortable" onclick="Suppliers.sortBy('supplier_code')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    رقم المورد
                                </th>
                                <th class="sortable" onclick="Suppliers.sortBy('name')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    اسم المورد
                                </th>
                                <th class="sortable" onclick="Suppliers.sortBy('supplier_type')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    نوع الخدمة
                                </th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المدينة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table-body">
                            ${this.renderTableRows()}
                        </tbody>
                    </table>

                    <!-- ترقيم الصفحات -->
                    <div class="table-pagination">
                        <div class="pagination-info">
                            عرض ${this.getPaginationInfo()}
                        </div>
                        <div class="pagination-controls">
                            ${this.renderPagination()}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل مورد -->
            <div class="modal" id="supplier-modal">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3 class="modal-title" id="supplier-modal-title">إضافة مورد جديد</h3>
                        <button class="modal-close" onclick="Suppliers.hideModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${this.renderSupplierForm()}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="Suppliers.hideModal()">إلغاء</button>
                        <button class="btn btn-primary" onclick="Suppliers.saveSupplier()">حفظ</button>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الإحصائيات
     */
    renderStats: function() {
        const stats = this.calculateStats();
        
        return `
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">إجمالي الموردين</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 8%
                    </div>
                </div>
            </div>
            
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.active}</div>
                    <div class="stat-label">نشط</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 5%
                    </div>
                </div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.featured}</div>
                    <div class="stat-label">مميز</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 20%
                    </div>
                </div>
            </div>
            
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.totalDeals}</div>
                    <div class="stat-label">إجمالي الصفقات</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 12%
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفلاتر
     */
    renderFilters: function() {
        return `
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">البحث بالاسم</label>
                    <input type="text" class="form-control" id="filter-name" 
                           placeholder="ابحث عن مورد..." onkeyup="Suppliers.applyFilters()">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">نوع الخدمة</label>
                    <select class="form-select" id="filter-service-type" onchange="Suppliers.applyFilters()">
                        <option value="">جميع الخدمات</option>
                        <option value="airline">طيران</option>
                        <option value="hotel">فنادق</option>
                        <option value="transport">نقل</option>
                        <option value="visa">تأشيرات</option>
                        <option value="insurance">تأمين</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">حالة المورد</label>
                    <select class="form-select" id="filter-status" onchange="Suppliers.applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">المدينة</label>
                    <select class="form-select" id="filter-city" onchange="Suppliers.applyFilters()">
                        <option value="">جميع المدن</option>
                        ${this.getCitiesOptions()}
                    </select>
                </div>
            </div>
            
            <div class="filters-actions">
                <button class="btn btn-primary" onclick="Suppliers.applyFilters()">
                    <i class="fas fa-filter"></i>
                    تطبيق الفلتر
                </button>
                <button class="btn btn-secondary" onclick="Suppliers.clearFilters()">
                    <i class="fas fa-eraser"></i>
                    مسح الفلتر
                </button>
            </div>
        `;
    },

    /**
     * عرض صفوف الجدول
     */
    renderTableRows: function() {
        if (this.data.filteredSuppliers.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد موردين</p>
                    </td>
                </tr>
            `;
        }

        const startIndex = (this.data.currentPage - 1) * this.data.itemsPerPage;
        const endIndex = startIndex + this.data.itemsPerPage;
        const pageSuppliers = this.data.filteredSuppliers.slice(startIndex, endIndex);

        return pageSuppliers.map((supplier, index) => `
            <tr>
                <td>${startIndex + index + 1}</td>
                <td><strong>${supplier.supplier_code || 'غير محدد'}</strong></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="item-avatar me-3">
                            ${supplier.name ? supplier.name.charAt(0) : 'م'}
                        </div>
                        <div>
                            <div class="fw-semibold">${supplier.name || 'غير محدد'}</div>
                            <small class="text-muted">${supplier.contact_person || ''}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge badge-${this.getServiceTypeClass(supplier.supplier_type)}">
                        ${this.getServiceTypeName(supplier.supplier_type)}
                    </span>
                </td>
                <td>${supplier.phone || 'غير محدد'}</td>
                <td>${supplier.email || 'غير محدد'}</td>
                <td>${supplier.city || 'غير محدد'}</td>
                <td>
                    <span class="table-status ${supplier.status === 'active' ? 'active' : 'inactive'}">
                        <span class="status-dot"></span>
                        ${supplier.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="table-cell-actions">
                        <button class="cell-action view" onclick="Suppliers.viewSupplier('${supplier.id}')" 
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="cell-action edit" onclick="Suppliers.editSupplier('${supplier.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="cell-action delete" onclick="Suppliers.deleteSupplier('${supplier.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض نموذج المورد
     */
    renderSupplierForm: function() {
        return `
            <form id="supplier-form" class="advanced-form">
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-building form-section-icon"></i>
                        بيانات المورد
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label form-label-required">اسم المورد</label>
                            <input type="text" class="form-control" id="supplier-name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="supplier-contact-person">
                        </div>
                        <div class="form-group">
                            <label class="form-label form-label-required">نوع الخدمة</label>
                            <select class="form-select" id="supplier-type" required>
                                <option value="">اختر نوع الخدمة</option>
                                <option value="airline">طيران</option>
                                <option value="hotel">فنادق</option>
                                <option value="transport">نقل</option>
                                <option value="visa">تأشيرات</option>
                                <option value="insurance">تأمين</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="supplier-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-phone form-section-icon"></i>
                        معلومات الاتصال
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="supplier-phone">
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="supplier-email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="supplier-city">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="supplier-country" value="السعودية">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="supplier-address" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-file-contract form-section-icon"></i>
                        شروط التعامل
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">شروط الدفع</label>
                            <input type="text" class="form-control" id="supplier-payment-terms" 
                                   placeholder="مثال: 30 يوم">
                        </div>
                        <div class="form-group">
                            <label class="form-label">حد الائتمان</label>
                            <input type="number" class="form-control" id="supplier-credit-limit" 
                                   min="0" step="0.01">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="supplier-notes" rows="3"></textarea>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة مكون الموردين
     */
    init: function(params = {}) {
        console.log('🔄 تهيئة مكون الموردين');

        // تحميل البيانات
        this.loadData();

        // تطبيق الفلاتر
        this.applyFilters();

        console.log('✅ تم تهيئة مكون الموردين');
    },

    /**
     * تحميل بيانات الموردين
     */
    loadData: function() {
        if (window.Database) {
            this.data.suppliers = window.Database.findAll('suppliers');
        } else {
            this.data.suppliers = [];
        }

        this.data.filteredSuppliers = [...this.data.suppliers];
        this.calculatePagination();
    },

    /**
     * حساب الإحصائيات
     */
    calculateStats: function() {
        const total = this.data.suppliers.length;
        const active = this.data.suppliers.filter(s => s.status === 'active').length;
        const featured = this.data.suppliers.filter(s => s.is_featured).length;
        const totalDeals = 156; // يمكن حسابها من قاعدة البيانات

        return { total, active, featured, totalDeals };
    },

    /**
     * الحصول على خيارات المدن
     */
    getCitiesOptions: function() {
        const cities = [...new Set(this.data.suppliers.map(s => s.city).filter(Boolean))];
        return cities.map(city => `<option value="${city}">${city}</option>`).join('');
    },

    /**
     * الحصول على فئة نوع الخدمة
     */
    getServiceTypeClass: function(type) {
        const typeMap = {
            'airline': 'primary',
            'hotel': 'success',
            'transport': 'warning',
            'visa': 'info',
            'insurance': 'secondary',
            'other': 'dark'
        };
        return typeMap[type] || 'secondary';
    },

    /**
     * الحصول على اسم نوع الخدمة
     */
    getServiceTypeName: function(type) {
        const typeMap = {
            'airline': 'طيران',
            'hotel': 'فنادق',
            'transport': 'نقل',
            'visa': 'تأشيرات',
            'insurance': 'تأمين',
            'other': 'أخرى'
        };
        return typeMap[type] || 'غير محدد';
    },

    /**
     * عرض ترقيم الصفحات
     */
    renderPagination: function() {
        if (this.data.totalPages <= 1) return '';

        let pagination = '';

        // زر السابق
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === 1 ? 'disabled' : ''}"
                    onclick="Suppliers.goToPage(${this.data.currentPage - 1})"
                    ${this.data.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.data.totalPages; i++) {
            if (i === this.data.currentPage ||
                i === 1 ||
                i === this.data.totalPages ||
                (i >= this.data.currentPage - 1 && i <= this.data.currentPage + 1)) {

                pagination += `
                    <button class="pagination-btn ${i === this.data.currentPage ? 'active' : ''}"
                            onclick="Suppliers.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.data.currentPage - 2 || i === this.data.currentPage + 2) {
                pagination += '<span class="pagination-ellipsis">...</span>';
            }
        }

        // زر التالي
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}"
                    onclick="Suppliers.goToPage(${this.data.currentPage + 1})"
                    ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        return pagination;
    },

    /**
     * الحصول على معلومات الترقيم
     */
    getPaginationInfo: function() {
        const start = (this.data.currentPage - 1) * this.data.itemsPerPage + 1;
        const end = Math.min(start + this.data.itemsPerPage - 1, this.data.filteredSuppliers.length);
        const total = this.data.filteredSuppliers.length;

        return `${start} - ${end} من ${total} مورد`;
    },

    /**
     * حساب الترقيم
     */
    calculatePagination: function() {
        this.data.totalPages = Math.ceil(this.data.filteredSuppliers.length / this.data.itemsPerPage);
        if (this.data.currentPage > this.data.totalPages) {
            this.data.currentPage = 1;
        }
    },

    /**
     * الانتقال لصفحة معينة
     */
    goToPage: function(page) {
        if (page >= 1 && page <= this.data.totalPages) {
            this.data.currentPage = page;
            this.updateTable();
        }
    },

    /**
     * تبديل عرض الفلاتر
     */
    toggleFilters: function() {
        const filtersBody = document.getElementById('suppliers-filters');
        const toggleBtn = document.querySelector('.filters-toggle');

        if (filtersBody.classList.contains('show')) {
            filtersBody.classList.remove('show');
            toggleBtn.innerHTML = '<span>إظهار الفلاتر</span><i class="fas fa-chevron-down"></i>';
        } else {
            filtersBody.classList.add('show');
            toggleBtn.innerHTML = '<span>إخفاء الفلاتر</span><i class="fas fa-chevron-up"></i>';
        }
    },

    /**
     * تطبيق الفلاتر
     */
    applyFilters: function() {
        // الحصول على قيم الفلاتر
        const nameFilter = document.getElementById('filter-name')?.value.toLowerCase() || '';
        const serviceTypeFilter = document.getElementById('filter-service-type')?.value || '';
        const statusFilter = document.getElementById('filter-status')?.value || '';
        const cityFilter = document.getElementById('filter-city')?.value || '';

        // تطبيق الفلاتر
        this.data.filteredSuppliers = this.data.suppliers.filter(supplier => {
            const matchesName = !nameFilter ||
                supplier.name?.toLowerCase().includes(nameFilter) ||
                supplier.supplier_code?.toLowerCase().includes(nameFilter);

            const matchesServiceType = !serviceTypeFilter || supplier.supplier_type === serviceTypeFilter;
            const matchesStatus = !statusFilter || supplier.status === statusFilter;
            const matchesCity = !cityFilter || supplier.city === cityFilter;

            return matchesName && matchesServiceType && matchesStatus && matchesCity;
        });

        // إعادة حساب الترقيم
        this.data.currentPage = 1;
        this.calculatePagination();

        // تحديث الجدول
        this.updateTable();
    },

    /**
     * مسح الفلاتر
     */
    clearFilters: function() {
        // مسح قيم الفلاتر
        const filterInputs = document.querySelectorAll('#suppliers-filters input, #suppliers-filters select');
        filterInputs.forEach(input => {
            input.value = '';
        });

        // إعادة تطبيق الفلاتر
        this.applyFilters();
    },

    /**
     * البحث في الموردين
     */
    handleSearch: function(searchTerm) {
        const nameFilter = document.getElementById('filter-name');
        if (nameFilter) {
            nameFilter.value = searchTerm;
            this.applyFilters();
        }
    },

    /**
     * ترتيب البيانات
     */
    sortBy: function(field) {
        if (this.data.sortBy === field) {
            this.data.sortOrder = this.data.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.data.sortBy = field;
            this.data.sortOrder = 'asc';
        }

        this.data.filteredSuppliers.sort((a, b) => {
            let aValue = a[field] || '';
            let bValue = b[field] || '';

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.data.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.updateTable();
        this.updateSortIcons(field);
    },

    /**
     * تحديث أيقونات الترتيب
     */
    updateSortIcons: function(activeField) {
        const sortIcons = document.querySelectorAll('.sort-icon');
        sortIcons.forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
            icon.parentElement.classList.remove('sorted');
        });

        const activeIcon = document.querySelector(`th[onclick="Suppliers.sortBy('${activeField}')"] .sort-icon`);
        if (activeIcon) {
            activeIcon.className = `fas fa-sort-${this.data.sortOrder === 'asc' ? 'up' : 'down'} sort-icon`;
            activeIcon.parentElement.classList.add('sorted');
        }
    },

    /**
     * تحديث الجدول
     */
    updateTable: function() {
        const tableBody = document.getElementById('suppliers-table-body');
        const paginationInfo = document.querySelector('.pagination-info');
        const paginationControls = document.querySelector('.pagination-controls');

        if (tableBody) {
            tableBody.innerHTML = this.renderTableRows();
        }

        if (paginationInfo) {
            paginationInfo.innerHTML = `عرض ${this.getPaginationInfo()}`;
        }

        if (paginationControls) {
            paginationControls.innerHTML = this.renderPagination();
        }
    },

    /**
     * عرض نافذة إضافة مورد
     */
    showAddModal: function() {
        const modal = document.getElementById('supplier-modal');
        const title = document.getElementById('supplier-modal-title');

        title.textContent = 'إضافة مورد جديد';
        this.clearForm();
        modal.classList.add('show');
    },

    /**
     * عرض نافذة تعديل مورد
     */
    editSupplier: function(supplierId) {
        const supplier = this.data.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const modal = document.getElementById('supplier-modal');
        const title = document.getElementById('supplier-modal-title');

        title.textContent = 'تعديل بيانات المورد';
        this.fillForm(supplier);
        modal.classList.add('show');
    },

    /**
     * عرض تفاصيل المورد
     */
    viewSupplier: function(supplierId) {
        const supplier = this.data.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        // يمكن إضافة نافذة عرض التفاصيل هنا
        console.log('عرض تفاصيل المورد:', supplier);
    },

    /**
     * حذف مورد
     */
    deleteSupplier: function(supplierId) {
        if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
            if (window.Database) {
                window.Database.delete('suppliers', supplierId);
                this.loadData();
                this.applyFilters();

                if (window.Notifications) {
                    window.Notifications.success('تم حذف المورد بنجاح');
                }
            }
        }
    },

    /**
     * إخفاء النافذة المنبثقة
     */
    hideModal: function() {
        const modal = document.getElementById('supplier-modal');
        modal.classList.remove('show');
    },

    /**
     * حفظ بيانات المورد
     */
    saveSupplier: function() {
        const form = document.getElementById('supplier-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const supplierData = this.getFormData();

        if (window.Database) {
            // إضافة رقم مورد تلقائي
            if (!supplierData.supplier_code) {
                supplierData.supplier_code = this.generateSupplierCode();
            }

            window.Database.insert('suppliers', supplierData);
            this.loadData();
            this.applyFilters();
            this.hideModal();

            if (window.Notifications) {
                window.Notifications.success('تم حفظ بيانات المورد بنجاح');
            }
        }
    },

    /**
     * الحصول على بيانات النموذج
     */
    getFormData: function() {
        return {
            name: document.getElementById('supplier-name')?.value || '',
            contact_person: document.getElementById('supplier-contact-person')?.value || '',
            supplier_type: document.getElementById('supplier-type')?.value || '',
            status: document.getElementById('supplier-status')?.value || 'active',
            phone: document.getElementById('supplier-phone')?.value || '',
            email: document.getElementById('supplier-email')?.value || '',
            city: document.getElementById('supplier-city')?.value || '',
            country: document.getElementById('supplier-country')?.value || 'السعودية',
            address: document.getElementById('supplier-address')?.value || '',
            payment_terms: document.getElementById('supplier-payment-terms')?.value || '',
            credit_limit: parseFloat(document.getElementById('supplier-credit-limit')?.value) || 0,
            notes: document.getElementById('supplier-notes')?.value || ''
        };
    },

    /**
     * ملء النموذج ببيانات المورد
     */
    fillForm: function(supplier) {
        document.getElementById('supplier-name').value = supplier.name || '';
        document.getElementById('supplier-contact-person').value = supplier.contact_person || '';
        document.getElementById('supplier-type').value = supplier.supplier_type || '';
        document.getElementById('supplier-status').value = supplier.status || 'active';
        document.getElementById('supplier-phone').value = supplier.phone || '';
        document.getElementById('supplier-email').value = supplier.email || '';
        document.getElementById('supplier-city').value = supplier.city || '';
        document.getElementById('supplier-country').value = supplier.country || 'السعودية';
        document.getElementById('supplier-address').value = supplier.address || '';
        document.getElementById('supplier-payment-terms').value = supplier.payment_terms || '';
        document.getElementById('supplier-credit-limit').value = supplier.credit_limit || 0;
        document.getElementById('supplier-notes').value = supplier.notes || '';
    },

    /**
     * مسح النموذج
     */
    clearForm: function() {
        const form = document.getElementById('supplier-form');
        if (form) {
            form.reset();
        }
    },

    /**
     * إنشاء رقم مورد
     */
    generateSupplierCode: function() {
        const prefix = 'SUP';
        const timestamp = Date.now().toString().slice(-6);
        return `${prefix}${timestamp}`;
    },

    /**
     * تصدير البيانات
     */
    exportData: function() {
        if (window.Helpers) {
            const headers = ['رقم المورد', 'اسم المورد', 'نوع الخدمة', 'الهاتف', 'البريد الإلكتروني', 'المدينة', 'الحالة'];
            const data = this.data.filteredSuppliers.map(supplier => [
                supplier.supplier_code || '',
                supplier.name || '',
                this.getServiceTypeName(supplier.supplier_type),
                supplier.phone || '',
                supplier.email || '',
                supplier.city || '',
                supplier.status === 'active' ? 'نشط' : 'غير نشط'
            ]);

            window.Helpers.downloadCSV(data, 'suppliers.csv', headers);

            if (window.Notifications) {
                window.Notifications.success('تم تصدير بيانات الموردين بنجاح');
            }
        }
    }
};

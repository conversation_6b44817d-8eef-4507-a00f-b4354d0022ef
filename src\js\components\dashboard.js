/**
 * ===================================
 * مكون لوحة التحكم - Dashboard Component
 * ===================================
 */

window.Dashboard = {
    // بيانات لوحة التحكم
    data: {
        stats: {},
        recentActivity: [],
        quickActions: []
    },

    /**
     * عرض لوحة التحكم
     */
    render: function(params = {}) {
        return `
            <div class="dashboard-container">
                <!-- عنوان الصفحة -->
                <div class="page-header">
                    <h1>
                        <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                    <p class="lead">نظام إدارة وكالة السفر المتكامل</p>
                </div>

                <!-- الوحدات الرئيسية -->
                <div class="modules-grid" id="modules-grid">
                    ${this.renderModules()}
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions" id="quick-actions">
                    <div class="quick-actions-header">
                        <div class="quick-actions-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="quick-actions-title">الإجراءات السريعة</h3>
                    </div>
                    <div class="quick-actions-grid">
                        ${this.renderQuickActions()}
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section" id="stats-section">
                    <div class="stats-grid">
                        ${this.renderStats()}
                    </div>
                </div>

                <!-- النشاط الأخير -->
                <div class="activity-section" id="activity-section">
                    <div class="activity-header">
                        <div class="activity-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 class="activity-title">النشاط الأخير</h3>
                    </div>
                    <div class="activity-list">
                        ${this.renderRecentActivity()}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الوحدات الرئيسية
     */
    renderModules: function() {
        const modules = [
            {
                id: 'customers',
                title: 'إدارة العملاء',
                description: 'إدارة بيانات العملاء والمعاملات والتأشيرات',
                icon: 'fas fa-users',
                color: 'primary',
                stats: this.getCustomersCount(),
                action: 'showCustomers'
            },
            {
                id: 'suppliers',
                title: 'إدارة الموردين',
                description: 'إدارة الموردين والشركاء وأنواع الخدمات',
                icon: 'fas fa-truck',
                color: 'success',
                stats: this.getSuppliersCount(),
                action: 'showSuppliers'
            },
            {
                id: 'agents',
                title: 'إدارة الوكلاء',
                description: 'إدارة الوكلاء والمندوبين والعمولات',
                icon: 'fas fa-handshake',
                color: 'info',
                stats: this.getAgentsCount(),
                action: 'showAgents'
            },
            {
                id: 'bookings',
                title: 'الحجوزات',
                description: 'إدارة الحجوزات والرحلات والفنادق',
                icon: 'fas fa-calendar-check',
                color: 'warning',
                stats: this.getBookingsCount(),
                action: 'showBookings'
            },
            {
                id: 'visa-inventory',
                title: 'مخزون التأشيرات',
                description: 'إدارة التأشيرات والوثائق الرسمية',
                icon: 'fas fa-passport',
                color: 'purple',
                stats: this.getVisaInventoryCount(),
                action: 'showVisaInventory'
            },
            {
                id: 'accounting',
                title: 'الحسابات',
                description: 'النظام المحاسبي والمالي والتقارير',
                icon: 'fas fa-calculator',
                color: 'dark',
                stats: this.getAccountingStats(),
                action: 'showAccounts'
            }
        ];

        return modules.map(module => `
            <div class="module-card ${module.color}" onclick="${module.action}()">
                <div class="module-icon">
                    <i class="${module.icon}"></i>
                </div>
                <div class="module-content">
                    <h3 class="module-title">${module.title}</h3>
                    <p class="module-description">${module.description}</p>
                    <div class="module-stats">
                        <span class="stat-number">${module.stats.count}</span>
                        <span class="stat-label">${module.stats.label}</span>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * عرض الإجراءات السريعة
     */
    renderQuickActions: function() {
        const quickActions = [
            {
                title: 'إضافة عميل',
                icon: 'fas fa-user-plus',
                action: 'showAddCustomer'
            },
            {
                title: 'حجز جديد',
                icon: 'fas fa-calendar-plus',
                action: 'showAddBooking'
            },
            {
                title: 'إضافة مورد',
                icon: 'fas fa-truck',
                action: 'showAddSupplier'
            },
            {
                title: 'إضافة وكيل',
                icon: 'fas fa-handshake',
                action: 'showAddAgent'
            },
            {
                title: 'إدارة التأشيرات',
                icon: 'fas fa-passport',
                action: 'showVisaInventory'
            },
            {
                title: 'قيد محاسبي',
                icon: 'fas fa-edit',
                action: 'showJournalEntries'
            }
        ];

        return quickActions.map(action => `
            <a href="#" class="quick-action-btn" onclick="${action.action}()">
                <div class="quick-action-icon">
                    <i class="${action.icon}"></i>
                </div>
                <div class="quick-action-text">${action.title}</div>
            </a>
        `).join('');
    },

    /**
     * عرض الإحصائيات
     */
    renderStats: function() {
        const stats = [
            {
                title: 'إجمالي العملاء',
                value: this.getCustomersCount().count,
                icon: 'fas fa-users',
                color: 'primary',
                trend: { direction: 'up', value: '8%' }
            },
            {
                title: 'الحجوزات النشطة',
                value: this.getActiveBookingsCount(),
                icon: 'fas fa-calendar-check',
                color: 'success',
                trend: { direction: 'up', value: '12%' }
            },
            {
                title: 'الإيرادات الشهرية',
                value: this.getMonthlyRevenue(),
                icon: 'fas fa-money-bill-wave',
                color: 'warning',
                trend: { direction: 'up', value: '5%' }
            },
            {
                title: 'التأشيرات المتاحة',
                value: this.getAvailableVisas(),
                icon: 'fas fa-passport',
                color: 'info',
                trend: { direction: 'down', value: '3%' }
            },
            {
                title: 'المعاملات اليوم',
                value: this.getTodayTransactions(),
                icon: 'fas fa-exchange-alt',
                color: 'purple',
                trend: { direction: 'up', value: '15%' }
            }
        ];

        return stats.map(stat => `
            <div class="stat-card ${stat.color}">
                <div class="stat-icon">
                    <i class="${stat.icon}"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stat.value}</div>
                    <div class="stat-label">${stat.title}</div>
                    <div class="stat-trend ${stat.trend.direction}">
                        <i class="fas fa-arrow-${stat.trend.direction}"></i>
                        ${stat.trend.value}
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * عرض النشاط الأخير
     */
    renderRecentActivity: function() {
        const activities = this.getRecentActivity();

        if (activities.length === 0) {
            return `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            `;
        }

        return activities.map(activity => `
            <div class="activity-item">
                <div class="activity-item-icon ${activity.color}">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-item-content">
                    <div class="activity-item-title">${activity.title}</div>
                    <div class="activity-item-time">${activity.time}</div>
                </div>
            </div>
        `).join('');
    },

    /**
     * تهيئة لوحة التحكم
     */
    init: function(params = {}) {
        console.log('🔄 تهيئة لوحة التحكم');
        
        // تحديث الإحصائيات
        this.updateStats();
        
        // تحديث النشاط الأخير
        this.updateRecentActivity();
        
        // بدء التحديث التلقائي
        this.startAutoRefresh();
        
        console.log('✅ تم تهيئة لوحة التحكم');
    },

    /**
     * تحديث الإحصائيات
     */
    updateStats: function() {
        // يمكن إضافة منطق تحديث الإحصائيات هنا
    },

    /**
     * تحديث النشاط الأخير
     */
    updateRecentActivity: function() {
        // يمكن إضافة منطق تحديث النشاط هنا
    },

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh: function() {
        // تحديث كل 5 دقائق
        setInterval(() => {
            this.updateStats();
            this.updateRecentActivity();
        }, 5 * 60 * 1000);
    },

    // دوال الحصول على البيانات
    getCustomersCount: function() {
        const customers = window.Database ? window.Database.findAll('customers') : [];
        return { count: customers.length, label: 'عميل' };
    },

    getSuppliersCount: function() {
        const suppliers = window.Database ? window.Database.findAll('suppliers') : [];
        return { count: suppliers.length, label: 'مورد' };
    },

    getAgentsCount: function() {
        const agents = window.Database ? window.Database.findAll('agents') : [];
        return { count: agents.length, label: 'وكيل' };
    },

    getBookingsCount: function() {
        const bookings = window.Database ? window.Database.findAll('bookings') : [];
        return { count: bookings.length, label: 'حجز' };
    },

    getVisaInventoryCount: function() {
        const visas = window.Database ? window.Database.findAll('visa_inventory') : [];
        return { count: visas.length, label: 'تأشيرة' };
    },

    getAccountingStats: function() {
        // حساب الرصيد الإجمالي
        return { count: '89K ر.س', label: 'رصيد' };
    },

    getActiveBookingsCount: function() {
        const bookings = window.Database ? window.Database.findAll('bookings') : [];
        const activeBookings = bookings.filter(booking => booking.status === 'active');
        return activeBookings.length;
    },

    getMonthlyRevenue: function() {
        // حساب الإيرادات الشهرية
        return '89,000 ر.س';
    },

    getAvailableVisas: function() {
        const visas = window.Database ? window.Database.findAll('visa_inventory') : [];
        const availableVisas = visas.filter(visa => visa.status === 'available');
        return availableVisas.length;
    },

    getTodayTransactions: function() {
        const today = new Date().toISOString().split('T')[0];
        const entries = window.Database ? window.Database.findAll('journal_entries') : [];
        const todayEntries = entries.filter(entry => 
            entry.created_at && entry.created_at.startsWith(today)
        );
        return todayEntries.length;
    },

    getRecentActivity: function() {
        // محاكاة النشاط الأخير
        return [
            {
                title: 'عميل جديد تم إضافته',
                time: 'منذ 5 دقائق',
                icon: 'fas fa-user-plus',
                color: 'primary'
            },
            {
                title: 'حجز جديد تم إنشاؤه',
                time: 'منذ 15 دقيقة',
                icon: 'fas fa-calendar-check',
                color: 'success'
            },
            {
                title: 'تأشيرة تم تأشيرها',
                time: 'منذ 30 دقيقة',
                icon: 'fas fa-passport',
                color: 'info'
            },
            {
                title: 'دفعة جديدة تم استلامها',
                time: 'منذ ساعة',
                icon: 'fas fa-money-bill',
                color: 'warning'
            }
        ];
    }
};

// دوال التنقل العامة
function showCustomers() {
    if (window.App) {
        window.App.navigateTo('customers');
    }
}

function showSuppliers() {
    if (window.App) {
        window.App.navigateTo('suppliers');
    }
}

function showAgents() {
    if (window.App) {
        window.App.navigateTo('agents');
    }
}

function showBookings() {
    if (window.App) {
        window.App.navigateTo('bookings');
    }
}

function showVisaInventory() {
    if (window.App) {
        window.App.navigateTo('inventory');
    }
}

function showAccounts() {
    if (window.App) {
        window.App.navigateTo('accounting');
    }
}

function showDashboard() {
    if (window.App) {
        window.App.navigateTo('dashboard');
    }
}

// دوال الإجراءات السريعة
function showAddCustomer() {
    // يمكن إضافة منطق إضافة عميل هنا
    console.log('إضافة عميل جديد');
}

function showAddBooking() {
    // يمكن إضافة منطق إضافة حجز هنا
    console.log('إضافة حجز جديد');
}

function showAddSupplier() {
    // يمكن إضافة منطق إضافة مورد هنا
    console.log('إضافة مورد جديد');
}

function showAddAgent() {
    // يمكن إضافة منطق إضافة وكيل هنا
    console.log('إضافة وكيل جديد');
}

function showJournalEntries() {
    // يمكن إضافة منطق القيود المحاسبية هنا
    console.log('القيود المحاسبية');
}

/**
 * أنماط نظام المشتريات
 * Purchases System Styles
 */

/* المتغيرات */
:root {
    --purchases-primary: #007bff;
    --purchases-secondary: #6c757d;
    --purchases-success: #28a745;
    --purchases-info: #17a2b8;
    --purchases-warning: #ffc107;
    --purchases-danger: #dc3545;
    --purchases-light: #f8f9fa;
    --purchases-dark: #343a40;
    --purchases-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* نافذة المشتريات الرئيسية */
.purchases-management {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Cairo', sans-serif;
}

/* شريط التنقل */
.purchases-navbar {
    background: var(--purchases-gradient);
    padding: 15px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.purchases-brand h4 {
    color: white;
    margin: 0;
    font-weight: 600;
}

.purchases-nav-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    text-decoration: none;
    cursor: pointer;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: white;
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nav-btn i {
    font-size: 16px;
}

.nav-btn span {
    font-weight: 500;
}

/* المحتوى الرئيسي */
.purchases-content {
    padding: 0 20px 20px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: var(--purchases-gradient);
    border: none;
    padding: 15px 20px;
}

.card-header h6 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* بطاقات الإحصائيات */
.bg-gradient {
    background: linear-gradient(45deg, var(--bs-bg-opacity, 1), rgba(255, 255, 255, 0.1)) !important;
}

.bg-primary.bg-gradient {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.bg-success.bg-gradient {
    background: linear-gradient(45deg, #28a745, #1e7e34) !important;
}

.bg-warning.bg-gradient {
    background: linear-gradient(45deg, #ffc107, #e0a800) !important;
}

.bg-danger.bg-gradient {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.bg-info.bg-gradient {
    background: linear-gradient(45deg, #17a2b8, #138496) !important;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--purchases-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* الأزرار */
.btn {
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-color: #007bff;
}

.btn-outline-success {
    border-color: #28a745;
    color: #28a745;
}

.btn-outline-success:hover {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border-color: #28a745;
}

.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background: linear-gradient(45deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
}

.btn-outline-warning:hover {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border-color: #ffc107;
    color: white;
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.btn-outline-danger:hover {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border-color: #dc3545;
}

/* مجموعات الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.btn-group .btn:last-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

/* الشارات */
.badge {
    border-radius: 8px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 12px;
}

/* حقول الإدخال */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--purchases-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    background: var(--purchases-light);
    color: var(--purchases-dark);
}

/* التسميات */
.form-label {
    font-weight: 600;
    color: var(--purchases-dark);
    margin-bottom: 8px;
}

/* الأفاتار */
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-info {
    background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(19, 132, 150, 0.1));
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(30, 126, 52, 0.1));
    color: #155724;
}

.alert-warning {
    background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), rgba(224, 168, 0, 0.1));
    color: #856404;
}

.alert-danger {
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), rgba(200, 35, 51, 0.1));
    color: #721c24;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* التجاوب */
@media (max-width: 768px) {
    .purchases-nav-links {
        justify-content: center;
    }
    
    .nav-btn {
        font-size: 12px;
        padding: 8px 12px;
    }
    
    .nav-btn span {
        display: none;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .btn-group .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .purchases-content {
        padding: 0 10px 10px;
    }
    
    .purchases-navbar {
        padding: 10px 0;
    }
    
    .purchases-brand h4 {
        font-size: 18px;
    }
    
    .nav-btn {
        padding: 6px 8px;
    }
    
    .card {
        margin-bottom: 15px;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: var(--purchases-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15) !important;
}

/* تأثيرات التمرير */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* تحسينات الطباعة */
@media print {
    .purchases-navbar,
    .btn,
    .btn-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .table {
        font-size: 12px;
    }
}

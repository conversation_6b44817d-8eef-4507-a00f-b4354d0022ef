/**
 * ===================================
 * JavaScript لمحرر القوالب المحاسبية
 * Template Editor JavaScript
 * ===================================
 */

// المتغيرات العامة
let currentTemplate = null;
let selectedElement = null;
let templateData = {};
let isEditMode = false;

// تهيئة المحرر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeTemplateEditor();
    loadTemplateFromURL();
    setupEventListeners();
    updateElementsList();
});

/**
 * تهيئة محرر القوالب
 */
function initializeTemplateEditor() {
    console.log('تم تحميل محرر القوالب بنجاح');
    
    // إعداد العناصر القابلة للتحرير
    setupEditableElements();
    
    // إعداد السحب والإفلات
    setupDragAndDrop();
    
    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts();
}

/**
 * تحميل القالب من URL
 */
function loadTemplateFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const templateId = urlParams.get('template');
    const mode = urlParams.get('mode');
    
    if (templateId) {
        loadTemplate(templateId);
    }
    
    if (mode === 'edit') {
        isEditMode = true;
        enableEditMode();
    } else {
        isEditMode = false;
        enableViewMode();
    }
}

/**
 * تحميل قالب محدد
 */
function loadTemplate(templateId) {
    // محاكاة تحميل البيانات من الخادم
    console.log(`تحميل القالب: ${templateId}`);
    
    // يمكن إضافة استدعاء API هنا لتحميل بيانات القالب
    currentTemplate = templateId;
    
    // تحديث واجهة المستخدم
    updateTemplateTitle(templateId);
}

/**
 * تحديث عنوان القالب
 */
function updateTemplateTitle(templateId) {
    const title = document.querySelector('title');
    if (title) {
        title.textContent = `محرر القوالب - ${templateId} - نظام قيمة الوعد`;
    }
}

/**
 * إعداد العناصر القابلة للتحرير
 */
function setupEditableElements() {
    const editableElements = document.querySelectorAll('.editable-element');
    
    editableElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.stopPropagation();
            selectElement(this);
        });
        
        element.addEventListener('dblclick', function(e) {
            e.stopPropagation();
            if (isEditMode) {
                editElementContent(this);
            }
        });
    });
    
    // إلغاء التحديد عند النقر خارج العناصر
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.editable-element')) {
            deselectAllElements();
        }
    });
}

/**
 * تحديد عنصر
 */
function selectElement(element) {
    // إزالة التحديد من العناصر الأخرى
    deselectAllElements();
    
    // تحديد العنصر الحالي
    element.classList.add('selected');
    selectedElement = element;
    
    // تحديث لوحة الخصائص
    updatePropertiesPanel(element);
    
    // تحديث قائمة العناصر
    updateElementsList();
}

/**
 * إلغاء تحديد جميع العناصر
 */
function deselectAllElements() {
    const selectedElements = document.querySelectorAll('.editable-element.selected');
    selectedElements.forEach(element => {
        element.classList.remove('selected');
    });
    selectedElement = null;
    
    // مسح لوحة الخصائص
    clearPropertiesPanel();
}

/**
 * تحديث لوحة الخصائص
 */
function updatePropertiesPanel(element) {
    const propertiesContent = document.getElementById('propertiesContent');
    const elementType = element.dataset.type;
    
    let propertiesHTML = '';
    
    switch(elementType) {
        case 'header':
            propertiesHTML = createHeaderProperties(element);
            break;
        case 'title':
            propertiesHTML = createTitleProperties(element);
            break;
        case 'customer':
            propertiesHTML = createCustomerProperties(element);
            break;
        case 'amount':
            propertiesHTML = createAmountProperties(element);
            break;
        case 'signatures':
            propertiesHTML = createSignaturesProperties(element);
            break;
        default:
            propertiesHTML = createGeneralProperties(element);
    }
    
    propertiesContent.innerHTML = propertiesHTML;
    
    // إعداد مستمعي الأحداث للخصائص
    setupPropertiesEventListeners();
}

/**
 * إنشاء خصائص الرأس
 */
function createHeaderProperties(element) {
    return `
        <div class="property-group">
            <label>اسم الشركة</label>
            <input type="text" id="companyName" value="${getCompanyName(element)}" onchange="updateCompanyName(this.value)">
        </div>
        <div class="property-group">
            <label>تفاصيل الشركة</label>
            <textarea id="companyDetails" rows="3" onchange="updateCompanyDetails(this.value)">${getCompanyDetails(element)}</textarea>
        </div>
        <div class="property-group">
            <label>لون الخط</label>
            <input type="color" id="textColor" value="#1e293b" onchange="updateTextColor(this.value)">
        </div>
    `;
}

/**
 * إنشاء خصائص العنوان
 */
function createTitleProperties(element) {
    return `
        <div class="property-group">
            <label>نص العنوان</label>
            <input type="text" id="titleText" value="${getTitleText(element)}" onchange="updateTitleText(this.value)">
        </div>
        <div class="property-group">
            <label>حجم الخط</label>
            <select id="fontSize" onchange="updateFontSize(this.value)">
                <option value="1.5rem">صغير</option>
                <option value="2rem" selected>متوسط</option>
                <option value="2.5rem">كبير</option>
            </select>
        </div>
        <div class="property-group">
            <label>محاذاة النص</label>
            <select id="textAlign" onchange="updateTextAlign(this.value)">
                <option value="right">يمين</option>
                <option value="center" selected>وسط</option>
                <option value="left">يسار</option>
            </select>
        </div>
    `;
}

/**
 * إنشاء خصائص عامة
 */
function createGeneralProperties(element) {
    return `
        <div class="property-group">
            <label>نوع العنصر</label>
            <input type="text" value="${element.dataset.type}" readonly>
        </div>
        <div class="property-group">
            <label>الهامش العلوي</label>
            <input type="range" min="0" max="50" value="15" onchange="updateMarginTop(this.value)">
        </div>
        <div class="property-group">
            <label>الهامش السفلي</label>
            <input type="range" min="0" max="50" value="15" onchange="updateMarginBottom(this.value)">
        </div>
    `;
}

/**
 * مسح لوحة الخصائص
 */
function clearPropertiesPanel() {
    const propertiesContent = document.getElementById('propertiesContent');
    propertiesContent.innerHTML = '<p class="text-muted">اختر عنصراً لتحرير خصائصه</p>';
}

/**
 * تحديث قائمة العناصر
 */
function updateElementsList() {
    const elementsList = document.getElementById('elementsList');
    const editableElements = document.querySelectorAll('.editable-element');
    
    let elementsHTML = '';
    
    editableElements.forEach((element, index) => {
        const elementType = element.dataset.type;
        const isSelected = element.classList.contains('selected');
        const icon = getElementIcon(elementType);
        const name = getElementName(elementType);
        
        elementsHTML += `
            <div class="element-item ${isSelected ? 'active' : ''}" onclick="selectElementFromList(${index})">
                <i class="${icon}"></i>
                <span>${name}</span>
            </div>
        `;
    });
    
    elementsList.innerHTML = elementsHTML;
}

/**
 * الحصول على أيقونة العنصر
 */
function getElementIcon(type) {
    const icons = {
        header: 'fas fa-heading',
        title: 'fas fa-font',
        customer: 'fas fa-user',
        amount: 'fas fa-dollar-sign',
        words: 'fas fa-spell-check',
        signatures: 'fas fa-signature',
        footer: 'fas fa-align-center'
    };
    return icons[type] || 'fas fa-square';
}

/**
 * الحصول على اسم العنصر
 */
function getElementName(type) {
    const names = {
        header: 'رأس القالب',
        title: 'عنوان المستند',
        customer: 'معلومات العميل',
        amount: 'تفاصيل المبلغ',
        words: 'المبلغ بالأحرف',
        signatures: 'التوقيعات',
        footer: 'ذيل القالب'
    };
    return names[type] || type;
}

/**
 * تحديد عنصر من القائمة
 */
function selectElementFromList(index) {
    const editableElements = document.querySelectorAll('.editable-element');
    if (editableElements[index]) {
        selectElement(editableElements[index]);
        
        // التمرير إلى العنصر
        editableElements[index].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // إعداد أحداث شريط الأدوات
    setupToolbarEvents();
    
    // إعداد أحداث لوحة المفاتيح
    setupKeyboardEvents();
}

/**
 * إعداد أحداث شريط الأدوات
 */
function setupToolbarEvents() {
    // سيتم إضافة المزيد من الأحداث هنا
}

/**
 * إعداد أحداث لوحة المفاتيح
 */
function setupKeyboardEvents() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveTemplate();
        }
        
        // Ctrl+P للطباعة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printTemplate();
        }
        
        // Delete لحذف العنصر المحدد
        if (e.key === 'Delete' && selectedElement) {
            deleteSelectedElement();
        }
    });
}

// ===== وظائف الأدوات =====

/**
 * إضافة حقل نص
 */
function addTextField() {
    showNotification('سيتم إضافة حقل نص قريباً', 'info');
}

/**
 * إضافة حقل صورة
 */
function addImageField() {
    showNotification('سيتم إضافة حقل صورة قريباً', 'info');
}

/**
 * إضافة جدول
 */
function addTableField() {
    showNotification('سيتم إضافة جدول قريباً', 'info');
}

/**
 * إضافة خط
 */
function addLineField() {
    showNotification('سيتم إضافة خط قريباً', 'info');
}

/**
 * إضافة حقل بيانات
 */
function addDataField(fieldType) {
    showNotification(`سيتم إضافة حقل ${fieldType} قريباً`, 'info');
}

// ===== وظائف القالب =====

/**
 * حفظ القالب
 */
function saveTemplate() {
    showNotification('جاري حفظ القالب...', 'info');
    
    // محاكاة عملية الحفظ
    setTimeout(() => {
        showNotification('تم حفظ القالب بنجاح', 'success');
    }, 1000);
}

/**
 * معاينة القالب
 */
function previewTemplate() {
    const templateCanvas = document.getElementById('templateCanvas');
    const previewContent = document.getElementById('previewContent');
    
    // نسخ محتوى القالب للمعاينة
    previewContent.innerHTML = templateCanvas.innerHTML;
    
    // إزالة تأثيرات التحرير من المعاينة
    const editableElements = previewContent.querySelectorAll('.editable-element');
    editableElements.forEach(element => {
        element.classList.remove('selected', 'editable-element');
        element.style.border = 'none';
        element.style.background = 'transparent';
    });
    
    // فتح نافذة المعاينة
    const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
    previewModal.show();
}

/**
 * طباعة القالب
 */
function printTemplate() {
    window.print();
}

/**
 * طباعة المعاينة
 */
function printPreview() {
    const previewContent = document.getElementById('previewContent');
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة القالب</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            ${previewContent.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// ===== وظائف مساعدة =====

/**
 * الحصول على اسم الشركة
 */
function getCompanyName(element) {
    const nameElement = element.querySelector('.company-name');
    return nameElement ? nameElement.textContent : '';
}

/**
 * الحصول على تفاصيل الشركة
 */
function getCompanyDetails(element) {
    const detailsElement = element.querySelector('.company-details');
    return detailsElement ? detailsElement.textContent : '';
}

/**
 * الحصول على نص العنوان
 */
function getTitleText(element) {
    const titleElement = element.querySelector('h3');
    return titleElement ? titleElement.textContent : '';
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود
    if (window.showAdvancedNotification) {
        window.showAdvancedNotification(message, type);
    } else {
        alert(message);
    }
}

// ===== وظائف السحب والإفلات =====

/**
 * إعداد السحب والإفلات
 */
function setupDragAndDrop() {
    const editableElements = document.querySelectorAll('.editable-element');

    editableElements.forEach(element => {
        element.draggable = true;

        element.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', '');
            this.classList.add('dragging');
        });

        element.addEventListener('dragend', function(e) {
            this.classList.remove('dragging');
        });

        element.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        element.addEventListener('drop', function(e) {
            e.preventDefault();
            const draggingElement = document.querySelector('.dragging');
            if (draggingElement && draggingElement !== this) {
                swapElements(draggingElement, this);
                updateElementsList();
            }
        });
    });
}

/**
 * تبديل مواقع العناصر
 */
function swapElements(element1, element2) {
    const parent = element1.parentNode;
    const sibling = element1.nextSibling === element2 ? element1 : element1.nextSibling;

    element2.parentNode.insertBefore(element1, element2);
    parent.insertBefore(element2, sibling);

    showNotification('تم تغيير ترتيب العناصر', 'success');
}

// ===== وظائف اختصارات لوحة المفاتيح =====

/**
 * إعداد اختصارات لوحة المفاتيح
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+Z للتراجع
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            undoLastAction();
        }

        // Ctrl+Y للإعادة
        if (e.ctrlKey && e.key === 'y') {
            e.preventDefault();
            redoLastAction();
        }

        // Ctrl+C للنسخ
        if (e.ctrlKey && e.key === 'c' && selectedElement) {
            e.preventDefault();
            copyElement();
        }

        // Ctrl+V للصق
        if (e.ctrlKey && e.key === 'v') {
            e.preventDefault();
            pasteElement();
        }

        // Escape لإلغاء التحديد
        if (e.key === 'Escape') {
            deselectAllElements();
        }
    });
}

/**
 * التراجع عن آخر عملية
 */
function undoLastAction() {
    showNotification('سيتم إضافة وظيفة التراجع قريباً', 'info');
}

/**
 * إعادة آخر عملية
 */
function redoLastAction() {
    showNotification('سيتم إضافة وظيفة الإعادة قريباً', 'info');
}

/**
 * نسخ العنصر المحدد
 */
function copyElement() {
    if (selectedElement) {
        localStorage.setItem('copiedElement', selectedElement.outerHTML);
        showNotification('تم نسخ العنصر', 'success');
    }
}

/**
 * لصق العنصر المنسوخ
 */
function pasteElement() {
    const copiedElement = localStorage.getItem('copiedElement');
    if (copiedElement) {
        const templateCanvas = document.getElementById('templateCanvas');
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = copiedElement;
        const newElement = tempDiv.firstChild;

        if (selectedElement) {
            selectedElement.parentNode.insertBefore(newElement, selectedElement.nextSibling);
        } else {
            templateCanvas.appendChild(newElement);
        }

        setupEditableElements();
        updateElementsList();
        showNotification('تم لصق العنصر', 'success');
    } else {
        showNotification('لا يوجد عنصر منسوخ', 'warning');
    }
}

/**
 * حذف العنصر المحدد
 */
function deleteSelectedElement() {
    if (selectedElement) {
        const elementName = getElementName(selectedElement.dataset.type);
        if (confirm(`هل تريد حذف ${elementName}؟`)) {
            selectedElement.remove();
            selectedElement = null;
            updateElementsList();
            clearPropertiesPanel();
            showNotification('تم حذف العنصر', 'success');
        }
    }
}

// ===== وظائف تحديث الخصائص =====

/**
 * إعداد مستمعي الأحداث للخصائص
 */
function setupPropertiesEventListeners() {
    // إعداد أحداث تحديث الخصائص
    const propertyInputs = document.querySelectorAll('#propertiesContent input, #propertiesContent select, #propertiesContent textarea');

    propertyInputs.forEach(input => {
        input.addEventListener('change', function() {
            updateElementProperty(this.id, this.value);
        });

        input.addEventListener('input', function() {
            if (this.type === 'range' || this.type === 'color') {
                updateElementProperty(this.id, this.value);
            }
        });
    });
}

/**
 * تحديث خاصية العنصر
 */
function updateElementProperty(propertyId, value) {
    if (!selectedElement) return;

    switch(propertyId) {
        case 'companyName':
            updateCompanyName(value);
            break;
        case 'companyDetails':
            updateCompanyDetails(value);
            break;
        case 'textColor':
            updateTextColor(value);
            break;
        case 'titleText':
            updateTitleText(value);
            break;
        case 'fontSize':
            updateFontSize(value);
            break;
        case 'textAlign':
            updateTextAlign(value);
            break;
        default:
            console.log(`تحديث خاصية غير معروفة: ${propertyId}`);
    }
}

/**
 * تحديث اسم الشركة
 */
function updateCompanyName(value) {
    if (selectedElement) {
        const nameElement = selectedElement.querySelector('.company-name');
        if (nameElement) {
            nameElement.textContent = value;
            showNotification('تم تحديث اسم الشركة', 'success');
        }
    }
}

/**
 * تحديث تفاصيل الشركة
 */
function updateCompanyDetails(value) {
    if (selectedElement) {
        const detailsElement = selectedElement.querySelector('.company-details');
        if (detailsElement) {
            detailsElement.innerHTML = value.replace(/\n/g, '<br>');
            showNotification('تم تحديث تفاصيل الشركة', 'success');
        }
    }
}

/**
 * تحديث لون النص
 */
function updateTextColor(value) {
    if (selectedElement) {
        selectedElement.style.color = value;
        showNotification('تم تحديث لون النص', 'success');
    }
}

/**
 * تحديث نص العنوان
 */
function updateTitleText(value) {
    if (selectedElement) {
        const titleElement = selectedElement.querySelector('h3');
        if (titleElement) {
            titleElement.textContent = value;
            showNotification('تم تحديث نص العنوان', 'success');
        }
    }
}

/**
 * تحديث حجم الخط
 */
function updateFontSize(value) {
    if (selectedElement) {
        const titleElement = selectedElement.querySelector('h3');
        if (titleElement) {
            titleElement.style.fontSize = value;
            showNotification('تم تحديث حجم الخط', 'success');
        }
    }
}

/**
 * تحديث محاذاة النص
 */
function updateTextAlign(value) {
    if (selectedElement) {
        selectedElement.style.textAlign = value;
        showNotification('تم تحديث محاذاة النص', 'success');
    }
}

/**
 * تحديث الهامش العلوي
 */
function updateMarginTop(value) {
    if (selectedElement) {
        selectedElement.style.marginTop = value + 'px';
        showNotification(`تم تحديث الهامش العلوي: ${value}px`, 'success');
    }
}

/**
 * تحديث الهامش السفلي
 */
function updateMarginBottom(value) {
    if (selectedElement) {
        selectedElement.style.marginBottom = value + 'px';
        showNotification(`تم تحديث الهامش السفلي: ${value}px`, 'success');
    }
}

/**
 * تغيير حجم الصفحة
 */
function changePageSize(size) {
    const templateCanvas = document.getElementById('templateCanvas');

    switch(size) {
        case 'a4':
            templateCanvas.style.width = '210mm';
            templateCanvas.style.minHeight = '297mm';
            break;
        case 'a5':
            templateCanvas.style.width = '148mm';
            templateCanvas.style.minHeight = '210mm';
            break;
        case 'letter':
            templateCanvas.style.width = '216mm';
            templateCanvas.style.minHeight = '279mm';
            break;
    }

    showNotification(`تم تغيير حجم الصفحة إلى ${size.toUpperCase()}`, 'success');
}

/**
 * تغيير اتجاه الصفحة
 */
function changeOrientation(orientation) {
    const templateCanvas = document.getElementById('templateCanvas');

    if (orientation === 'landscape') {
        const currentWidth = templateCanvas.style.width;
        const currentHeight = templateCanvas.style.minHeight;

        templateCanvas.style.width = currentHeight;
        templateCanvas.style.minHeight = currentWidth;

        showNotification('تم تغيير الاتجاه إلى أفقي', 'success');
    } else {
        // إعادة تعيين الحجم الافتراضي
        changePageSize('a4');
        showNotification('تم تغيير الاتجاه إلى عمودي', 'success');
    }
}

// ===== وظائف المعاينة المتقدمة =====

/**
 * فتح معاينة متقدمة في نافذة جديدة
 */
function openAdvancedPreview() {
    showNotification('جاري فتح المعاينة المتقدمة...', 'info');

    // حفظ القالب الحالي قبل المعاينة
    saveTemplate();

    // فتح نافذة المعاينة المتقدمة
    setTimeout(() => {
        const templateId = getCurrentTemplateId();
        window.open(`template-preview.html?template=${templateId}&mode=preview`, '_blank');
    }, 500);
}

/**
 * الحصول على معرف القالب الحالي
 */
function getCurrentTemplateId() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('template') || 'default-template';
}

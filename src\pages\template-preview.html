<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة القالب - نظام قيمة الوعد</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../styles/main.css">
    <link rel="stylesheet" href="../css/accounting-enhanced.css">
    <link rel="stylesheet" href="../css/template-preview.css">
</head>
<body>
    <!-- شريط التقدم للتمرير -->
    <div class="scroll-progress" id="scrollProgress"></div>
    
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../index.html">
                <i class="fas fa-plane me-2"></i>
                قيمة الوعد
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="templates-management.html">
                    <i class="fas fa-arrow-right me-1"></i>العودة للقوالب
                </a>
            </div>
            
            <div class="navbar-nav">
                <button class="btn btn-outline-light me-2" onclick="toggleFullscreen()">
                    <i class="fas fa-expand me-1"></i>ملء الشاشة
                </button>
                <button class="btn btn-outline-light me-2" onclick="zoomIn()">
                    <i class="fas fa-search-plus me-1"></i>تكبير
                </button>
                <button class="btn btn-outline-light me-2" onclick="zoomOut()">
                    <i class="fas fa-search-minus me-1"></i>تصغير
                </button>
                <button class="btn btn-success me-2" onclick="printTemplate()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>PDF
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToWord()">
                            <i class="fas fa-file-word me-2"></i>Word
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToImage()">
                            <i class="fas fa-file-image me-2"></i>صورة
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="preview-container">
        <!-- شريط الأدوات -->
        <div class="preview-toolbar">
            <div class="toolbar-section">
                <h5><i class="fas fa-cog me-2"></i>إعدادات العرض</h5>
                <div class="toolbar-controls">
                    <div class="control-group">
                        <label>مستوى التكبير:</label>
                        <input type="range" id="zoomSlider" min="50" max="200" value="100" 
                               onchange="setZoom(this.value)" class="form-range">
                        <span id="zoomValue">100%</span>
                    </div>
                    
                    <div class="control-group">
                        <label>وضع العرض:</label>
                        <select class="form-select form-select-sm" onchange="changeViewMode(this.value)">
                            <option value="fit-width">ملء العرض</option>
                            <option value="fit-page">ملء الصفحة</option>
                            <option value="actual-size">الحجم الفعلي</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label>خلفية العرض:</label>
                        <select class="form-select form-select-sm" onchange="changeBackground(this.value)">
                            <option value="white">أبيض</option>
                            <option value="gray">رمادي</option>
                            <option value="dark">داكن</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="toolbar-section">
                <h5><i class="fas fa-eye me-2"></i>خيارات المعاينة</h5>
                <div class="toolbar-controls">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showRulers" onchange="toggleRulers()">
                        <label class="form-check-label" for="showRulers">إظهار المساطر</label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showGrid" onchange="toggleGrid()">
                        <label class="form-check-label" for="showGrid">إظهار الشبكة</label>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showMargins" onchange="toggleMargins()">
                        <label class="form-check-label" for="showMargins">إظهار الهوامش</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة المعاينة -->
        <div class="preview-area" id="previewArea">
            <div class="preview-page" id="previewPage">
                <!-- سيتم تحميل محتوى القالب هنا -->
                <div class="template-content" id="templateContent">
                    <!-- محتوى القالب الافتراضي -->
                    <div class="voucher-container">
                        <!-- رأس السند -->
                        <div class="voucher-header">
                            <div class="company-info">
                                <h1 class="company-name">شركة قيمة الوعد للسفريات</h1>
                                <p class="company-details">
                                    المملكة العربية السعودية - الرياض<br>
                                    هاتف: +966 11 123 4567 | البريد: <EMAIL><br>
                                    س.ت: 1234567890 | ض.ق: *********
                                </p>
                            </div>
                        </div>
                        
                        <!-- عنوان السند -->
                        <div class="voucher-title">
                            <h2>سند قبض</h2>
                            <div class="voucher-number">رقم السند: RV-2024-001</div>
                        </div>
                        
                        <!-- محتوى السند -->
                        <div class="voucher-body">
                            <!-- معلومات السند -->
                            <div class="voucher-info">
                                <div class="info-group">
                                    <div class="info-label">اسم العميل</div>
                                    <div class="info-value">أحمد محمد العلي</div>
                                </div>
                                <div class="info-group">
                                    <div class="info-label">تاريخ السند</div>
                                    <div class="info-value">15 يناير 2024</div>
                                </div>
                                <div class="info-group">
                                    <div class="info-label">رقم الهاتف</div>
                                    <div class="info-value">+966 50 123 4567</div>
                                </div>
                                <div class="info-group">
                                    <div class="info-label">طريقة الدفع</div>
                                    <div class="info-value">نقداً</div>
                                </div>
                            </div>
                            
                            <!-- قسم المبلغ -->
                            <div class="amount-section">
                                <div class="amount-label">المبلغ المستلم</div>
                                <div class="amount-value">5,250.00</div>
                                <div class="amount-currency">ريال سعودي</div>
                            </div>
                            
                            <!-- المبلغ بالأحرف -->
                            <div class="amount-words">
                                <div class="amount-words-label">المبلغ بالأحرف:</div>
                                <div class="amount-words-value">خمسة آلاف ومائتان وخمسون ريالاً سعودياً فقط لا غير</div>
                            </div>
                            
                            <!-- وصف السند -->
                            <div class="description-section">
                                <div class="description-label">البيان:</div>
                                <div class="description-value">
                                    دفعة مقدمة لحجز رحلة العمرة - باقة VIP<br>
                                    فترة السفر: 20-30 يناير 2024<br>
                                    عدد الأشخاص: 4 أشخاص
                                </div>
                            </div>
                            
                            <!-- التوقيعات -->
                            <div class="signatures-section">
                                <div class="signature-box">
                                    <div class="signature-title">المحاسب</div>
                                    <div class="signature-line"></div>
                                </div>
                                <div class="signature-box">
                                    <div class="signature-title">المدير المالي</div>
                                    <div class="signature-line"></div>
                                </div>
                                <div class="signature-box">
                                    <div class="signature-title">العميل</div>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ذيل السند -->
                        <div class="voucher-footer">
                            <p class="footer-text">شكراً لتعاملكم معنا - نتطلع لخدمتكم دائماً</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إعدادات الطباعة -->
    <div class="modal fade" id="printSettingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعدادات الطباعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">حجم الورق</label>
                        <select class="form-select" id="paperSize">
                            <option value="a4">A4</option>
                            <option value="a5">A5</option>
                            <option value="letter">Letter</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">اتجاه الطباعة</label>
                        <select class="form-select" id="orientation">
                            <option value="portrait">عمودي</option>
                            <option value="landscape">أفقي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الهوامش</label>
                        <select class="form-select" id="margins">
                            <option value="normal">عادي</option>
                            <option value="narrow">ضيق</option>
                            <option value="wide">واسع</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="printBackground">
                        <label class="form-check-label" for="printBackground">طباعة الخلفيات والألوان</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="confirmPrint()">طباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../js/template-preview.js"></script>
</body>
</html>

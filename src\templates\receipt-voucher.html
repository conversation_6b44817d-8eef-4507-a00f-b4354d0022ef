<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قالب سند القبض</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20mm;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .voucher-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            border: 2px solid #6366f1;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .voucher-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }
        
        .voucher-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .company-info {
            position: relative;
            z-index: 2;
        }
        
        .company-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .company-details {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .voucher-title {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 3px solid #6366f1;
            text-align: center;
        }
        
        .voucher-title h2 {
            color: #6366f1;
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }
        
        .voucher-number {
            background: #6366f1;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin-top: 1rem;
            font-weight: 600;
        }
        
        .voucher-body {
            padding: 2rem;
        }
        
        .voucher-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .info-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .info-label {
            font-weight: 600;
            color: #6366f1;
            font-size: 0.9rem;
        }
        
        .info-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #1e293b;
            padding: 0.5rem;
            background: white;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }
        
        .amount-section {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .amount-label {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }
        
        .amount-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .amount-currency {
            font-size: 1.1rem;
            opacity: 0.8;
        }
        
        .amount-words {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .amount-words-label {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 0.5rem;
        }
        
        .amount-words-value {
            font-size: 1.2rem;
            font-weight: 500;
            color: #451a03;
        }
        
        .description-section {
            background: #f1f5f9;
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #cbd5e1;
            margin-bottom: 2rem;
        }
        
        .description-label {
            font-weight: 600;
            color: #475569;
            margin-bottom: 0.5rem;
        }
        
        .description-value {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #334155;
        }
        
        .signatures-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .signature-box {
            text-align: center;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 10px;
            border: 2px dashed #cbd5e1;
        }
        
        .signature-title {
            font-weight: 600;
            color: #475569;
            margin-bottom: 2rem;
        }
        
        .signature-line {
            height: 2px;
            background: #6366f1;
            margin-top: 3rem;
            border-radius: 1px;
        }
        
        .voucher-footer {
            background: #1e293b;
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .footer-text {
            margin: 0;
            opacity: 0.8;
        }
        
        .print-date {
            position: absolute;
            top: 10mm;
            left: 10mm;
            font-size: 0.8rem;
            color: #64748b;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .print-date {
                display: block;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .voucher-info {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .signatures-section {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .company-name {
                font-size: 2rem;
            }
            
            .amount-value {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="print-date">
        تاريخ الطباعة: <span id="printDate"></span>
    </div>
    
    <div class="voucher-container">
        <!-- رأس السند -->
        <div class="voucher-header">
            <div class="company-info">
                <h1 class="company-name">شركة قيمة الوعد للسفريات</h1>
                <p class="company-details">
                    المملكة العربية السعودية - الرياض<br>
                    هاتف: +966 11 123 4567 | البريد: <EMAIL><br>
                    س.ت: 1234567890 | ض.ق: *********
                </p>
            </div>
        </div>
        
        <!-- عنوان السند -->
        <div class="voucher-title">
            <h2>سند قبض</h2>
            <div class="voucher-number">رقم السند: RV-2024-001</div>
        </div>
        
        <!-- محتوى السند -->
        <div class="voucher-body">
            <!-- معلومات السند -->
            <div class="voucher-info">
                <div class="info-group">
                    <div class="info-label">اسم العميل</div>
                    <div class="info-value">أحمد محمد العلي</div>
                </div>
                <div class="info-group">
                    <div class="info-label">تاريخ السند</div>
                    <div class="info-value">15 يناير 2024</div>
                </div>
                <div class="info-group">
                    <div class="info-label">رقم الهاتف</div>
                    <div class="info-value">+966 50 123 4567</div>
                </div>
                <div class="info-group">
                    <div class="info-label">طريقة الدفع</div>
                    <div class="info-value">نقداً</div>
                </div>
            </div>
            
            <!-- قسم المبلغ -->
            <div class="amount-section">
                <div class="amount-label">المبلغ المستلم</div>
                <div class="amount-value">5,250.00</div>
                <div class="amount-currency">ريال سعودي</div>
            </div>
            
            <!-- المبلغ بالأحرف -->
            <div class="amount-words">
                <div class="amount-words-label">المبلغ بالأحرف:</div>
                <div class="amount-words-value">خمسة آلاف ومائتان وخمسون ريالاً سعودياً فقط لا غير</div>
            </div>
            
            <!-- وصف السند -->
            <div class="description-section">
                <div class="description-label">البيان:</div>
                <div class="description-value">
                    دفعة مقدمة لحجز رحلة العمرة - باقة VIP<br>
                    فترة السفر: 20-30 يناير 2024<br>
                    عدد الأشخاص: 4 أشخاص
                </div>
            </div>
            
            <!-- التوقيعات -->
            <div class="signatures-section">
                <div class="signature-box">
                    <div class="signature-title">المحاسب</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature-box">
                    <div class="signature-title">المدير المالي</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature-box">
                    <div class="signature-title">العميل</div>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>
        
        <!-- ذيل السند -->
        <div class="voucher-footer">
            <p class="footer-text">شكراً لتعاملكم معنا - نتطلع لخدمتكم دائماً</p>
        </div>
    </div>
    
    <script>
        // تحديث تاريخ الطباعة
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-SA');
    </script>
</body>
</html>

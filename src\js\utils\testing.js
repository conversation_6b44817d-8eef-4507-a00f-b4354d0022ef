/**
 * ===================================
 * نظام الاختبار وضمان الجودة - Testing & QA System
 * ===================================
 */

window.Testing = {
    // إعدادات الاختبار
    config: {
        enableAutoTest: true,
        enablePerformanceTest: true,
        enableSecurityTest: true,
        testTimeout: 5000,
        maxRetries: 3
    },

    // حالة الاختبار
    state: {
        isRunning: false,
        currentTest: null,
        results: [],
        totalTests: 0,
        passedTests: 0,
        failedTests: 0
    },

    // مجموعات الاختبار
    testSuites: {
        core: [],
        components: [],
        utils: [],
        security: [],
        performance: []
    },

    /**
     * تهيئة نظام الاختبار
     */
    init: function() {
        console.log('🧪 تهيئة نظام الاختبار وضمان الجودة');
        
        try {
            // تسجيل الاختبارات
            this.registerTests();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
            console.log('✅ تم تهيئة نظام الاختبار بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الاختبار:', error);
        }
    },

    /**
     * تسجيل جميع الاختبارات
     */
    registerTests: function() {
        // اختبارات الأنظمة الأساسية
        this.registerCoreTests();
        
        // اختبارات المكونات
        this.registerComponentTests();
        
        // اختبارات الأدوات المساعدة
        this.registerUtilTests();
        
        // اختبارات الأمان
        this.registerSecurityTests();
        
        // اختبارات الأداء
        this.registerPerformanceTests();
    },

    /**
     * تسجيل اختبارات الأنظمة الأساسية
     */
    registerCoreTests: function() {
        // اختبار قاعدة البيانات
        this.addTest('core', 'database_crud', 'اختبار عمليات قاعدة البيانات', () => {
            const testData = { id: 'test123', name: 'اختبار', phone: '123456789' };
            
            // اختبار الإدراج
            const insertResult = window.Database.insert('test_table', testData);
            this.assert(insertResult, 'فشل في إدراج البيانات');
            
            // اختبار البحث
            const findResult = window.Database.find('test_table', 'test123');
            this.assert(findResult && findResult.name === 'اختبار', 'فشل في البحث عن البيانات');
            
            // اختبار التحديث
            const updateResult = window.Database.update('test_table', 'test123', { name: 'اختبار محدث' });
            this.assert(updateResult, 'فشل في تحديث البيانات');
            
            // اختبار الحذف
            const deleteResult = window.Database.delete('test_table', 'test123');
            this.assert(deleteResult, 'فشل في حذف البيانات');
            
            return true;
        });

        // اختبار نظام المصادقة
        this.addTest('core', 'auth_system', 'اختبار نظام المصادقة', () => {
            // اختبار تسجيل الدخول
            const loginResult = window.Auth.validateCredentials('admin', 'admin123');
            this.assert(loginResult, 'فشل في تسجيل الدخول');
            
            // اختبار تشفير كلمة المرور
            const hashedPassword = window.Auth.hashPassword('testpassword');
            this.assert(hashedPassword && hashedPassword.length > 0, 'فشل في تشفير كلمة المرور');
            
            return true;
        });

        // اختبار إدارة الحالة
        this.addTest('core', 'state_management', 'اختبار إدارة الحالة', () => {
            if (!window.State) return true; // تخطي إذا لم يكن متاحاً
            
            // اختبار تعيين القيمة
            window.State.set('test.value', 'test123');
            const getValue = window.State.get('test.value');
            this.assert(getValue === 'test123', 'فشل في تعيين/استرجاع القيمة');
            
            return true;
        });
    },

    /**
     * تسجيل اختبارات المكونات
     */
    registerComponentTests: function() {
        // اختبار مكون العملاء
        this.addTest('components', 'customers_component', 'اختبار مكون العملاء', () => {
            if (!window.Customers) return true;
            
            // اختبار عرض المكون
            const rendered = window.Customers.render();
            this.assert(rendered && rendered.includes('customers'), 'فشل في عرض مكون العملاء');
            
            return true;
        });

        // اختبار مكون الحجوزات
        this.addTest('components', 'bookings_component', 'اختبار مكون الحجوزات', () => {
            if (!window.Bookings) return true;
            
            const rendered = window.Bookings.render();
            this.assert(rendered && rendered.includes('bookings'), 'فشل في عرض مكون الحجوزات');
            
            return true;
        });
    },

    /**
     * تسجيل اختبارات الأدوات المساعدة
     */
    registerUtilTests: function() {
        // اختبار التحقق من البيانات
        this.addTest('utils', 'validation', 'اختبار التحقق من البيانات', () => {
            if (!window.Validation) return true;
            
            // اختبار البريد الإلكتروني
            this.assert(window.Validation.validateEmail('<EMAIL>'), 'فشل في التحقق من بريد صحيح');
            this.assert(!window.Validation.validateEmail('invalid-email'), 'فشل في رفض بريد خاطئ');
            
            // اختبار الهاتف
            this.assert(window.Validation.validatePhone('0501234567'), 'فشل في التحقق من رقم هاتف صحيح');
            this.assert(!window.Validation.validatePhone('123'), 'فشل في رفض رقم هاتف خاطئ');
            
            return true;
        });

        // اختبار البحث
        this.addTest('utils', 'search_system', 'اختبار نظام البحث', () => {
            if (!window.Search) return true;
            
            // اختبار تطبيع النص
            const normalized = window.Search.normalizeSearchTerm('  Test Text  ');
            this.assert(normalized === 'test text', 'فشل في تطبيع النص');
            
            return true;
        });

        // اختبار التخزين المؤقت
        this.addTest('utils', 'cache_system', 'اختبار نظام التخزين المؤقت', () => {
            if (!window.Cache) return true;
            
            // اختبار التخزين والاسترجاع
            window.Cache.set('test_key', 'test_value', 1000);
            const cachedValue = window.Cache.get('test_key');
            this.assert(cachedValue === 'test_value', 'فشل في التخزين المؤقت');
            
            return true;
        });
    },

    /**
     * تسجيل اختبارات الأمان
     */
    registerSecurityTests: function() {
        // اختبار تنظيف المدخلات
        this.addTest('security', 'input_sanitization', 'اختبار تنظيف المدخلات', () => {
            if (!window.Security) return true;
            
            const maliciousInput = '<script>alert("xss")</script>';
            const sanitized = window.Security.sanitizeInput(maliciousInput);
            this.assert(!sanitized.includes('<script>'), 'فشل في تنظيف المدخلات الضارة');
            
            return true;
        });

        // اختبار إنشاء التوكنات
        this.addTest('security', 'token_generation', 'اختبار إنشاء التوكنات', () => {
            if (!window.Security) return true;
            
            const token = window.Security.generateSecureToken(32);
            this.assert(token && token.length >= 32, 'فشل في إنشاء توكن آمن');
            
            return true;
        });
    },

    /**
     * تسجيل اختبارات الأداء
     */
    registerPerformanceTests: function() {
        // اختبار سرعة قاعدة البيانات
        this.addTest('performance', 'database_speed', 'اختبار سرعة قاعدة البيانات', () => {
            const startTime = performance.now();
            
            // إدراج 100 سجل
            for (let i = 0; i < 100; i++) {
                window.Database.insert('performance_test', {
                    id: `perf_${i}`,
                    name: `اختبار ${i}`,
                    data: `بيانات اختبار ${i}`
                });
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // يجب أن يكون أقل من ثانية واحدة
            this.assert(duration < 1000, `قاعدة البيانات بطيئة: ${duration}ms`);
            
            // تنظيف
            window.Database.data.performance_test = [];
            
            return true;
        });

        // اختبار استهلاك الذاكرة
        this.addTest('performance', 'memory_usage', 'اختبار استهلاك الذاكرة', () => {
            if (!performance.memory) return true;
            
            const memoryUsage = performance.memory.usedJSHeapSize;
            const maxAllowed = 100 * 1024 * 1024; // 100MB
            
            this.assert(memoryUsage < maxAllowed, `استهلاك ذاكرة عالي: ${this.formatBytes(memoryUsage)}`);
            
            return true;
        });
    },

    /**
     * إضافة اختبار جديد
     */
    addTest: function(suite, name, description, testFunction) {
        if (!this.testSuites[suite]) {
            this.testSuites[suite] = [];
        }
        
        this.testSuites[suite].push({
            name: name,
            description: description,
            test: testFunction,
            suite: suite
        });
        
        this.state.totalTests++;
    },

    /**
     * تشغيل جميع الاختبارات
     */
    runAllTests: function() {
        console.log('🧪 بدء تشغيل جميع الاختبارات...');
        
        this.state.isRunning = true;
        this.state.results = [];
        this.state.passedTests = 0;
        this.state.failedTests = 0;
        
        const startTime = performance.now();
        
        // تشغيل كل مجموعة اختبار
        Object.keys(this.testSuites).forEach(suiteName => {
            this.runTestSuite(suiteName);
        });
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // عرض النتائج
        this.displayResults(duration);
        
        this.state.isRunning = false;
        
        return this.state.passedTests === this.state.totalTests;
    },

    /**
     * تشغيل مجموعة اختبار محددة
     */
    runTestSuite: function(suiteName) {
        const suite = this.testSuites[suiteName];
        if (!suite) return;
        
        console.log(`📋 تشغيل مجموعة اختبار: ${suiteName}`);
        
        suite.forEach(test => {
            this.runSingleTest(test);
        });
    },

    /**
     * تشغيل اختبار واحد
     */
    runSingleTest: function(test) {
        this.state.currentTest = test;
        
        try {
            const startTime = performance.now();
            const result = test.test();
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (result) {
                this.state.passedTests++;
                this.state.results.push({
                    name: test.name,
                    description: test.description,
                    suite: test.suite,
                    status: 'passed',
                    duration: duration,
                    message: 'نجح الاختبار'
                });
                console.log(`✅ ${test.description} - ${duration.toFixed(2)}ms`);
            } else {
                this.state.failedTests++;
                this.state.results.push({
                    name: test.name,
                    description: test.description,
                    suite: test.suite,
                    status: 'failed',
                    duration: duration,
                    message: 'فشل الاختبار'
                });
                console.error(`❌ ${test.description}`);
            }
        } catch (error) {
            this.state.failedTests++;
            this.state.results.push({
                name: test.name,
                description: test.description,
                suite: test.suite,
                status: 'error',
                duration: 0,
                message: error.message
            });
            console.error(`💥 ${test.description} - خطأ: ${error.message}`);
        }
        
        this.state.currentTest = null;
    },

    /**
     * دالة التأكيد
     */
    assert: function(condition, message) {
        if (!condition) {
            throw new Error(message || 'فشل في التأكيد');
        }
        return true;
    },

    /**
     * عرض النتائج
     */
    displayResults: function(totalDuration) {
        console.log('\n📊 نتائج الاختبار:');
        console.log('================');
        console.log(`إجمالي الاختبارات: ${this.state.totalTests}`);
        console.log(`✅ نجح: ${this.state.passedTests}`);
        console.log(`❌ فشل: ${this.state.failedTests}`);
        console.log(`⏱️ الوقت الإجمالي: ${totalDuration.toFixed(2)}ms`);
        console.log(`📈 معدل النجاح: ${((this.state.passedTests / this.state.totalTests) * 100).toFixed(1)}%`);
        
        // عرض الاختبارات الفاشلة
        const failedTests = this.state.results.filter(r => r.status !== 'passed');
        if (failedTests.length > 0) {
            console.log('\n❌ الاختبارات الفاشلة:');
            failedTests.forEach(test => {
                console.log(`  - ${test.description}: ${test.message}`);
            });
        }
        
        // إرسال حدث النتائج
        this.emit('tests:completed', {
            total: this.state.totalTests,
            passed: this.state.passedTests,
            failed: this.state.failedTests,
            duration: totalDuration,
            results: this.state.results
        });
    },

    /**
     * اختبار الأداء المتقدم
     */
    runPerformanceBenchmark: function() {
        console.log('🚀 بدء اختبار الأداء المتقدم...');
        
        const benchmarks = {
            databaseOperations: this.benchmarkDatabase(),
            searchOperations: this.benchmarkSearch(),
            renderingSpeed: this.benchmarkRendering(),
            memoryUsage: this.benchmarkMemory()
        };
        
        console.log('📊 نتائج اختبار الأداء:', benchmarks);
        return benchmarks;
    },

    /**
     * اختبار أداء قاعدة البيانات
     */
    benchmarkDatabase: function() {
        const iterations = 1000;
        const startTime = performance.now();
        
        // اختبار الإدراج
        for (let i = 0; i < iterations; i++) {
            window.Database.insert('benchmark', {
                id: `bench_${i}`,
                data: `test data ${i}`
            });
        }
        
        // اختبار البحث
        for (let i = 0; i < 100; i++) {
            window.Database.find('benchmark', `bench_${i}`);
        }
        
        const endTime = performance.now();
        
        // تنظيف
        window.Database.data.benchmark = [];
        
        return {
            operations: iterations + 100,
            duration: endTime - startTime,
            opsPerSecond: ((iterations + 100) / ((endTime - startTime) / 1000)).toFixed(0)
        };
    },

    /**
     * اختبار أداء البحث
     */
    benchmarkSearch: function() {
        if (!window.Search) return { message: 'نظام البحث غير متاح' };
        
        const startTime = performance.now();
        const queries = ['test', 'اختبار', 'data', 'بيانات', 'user'];
        
        queries.forEach(query => {
            for (let i = 0; i < 100; i++) {
                window.Search.normalizeSearchTerm(query + i);
            }
        });
        
        const endTime = performance.now();
        
        return {
            queries: queries.length * 100,
            duration: endTime - startTime,
            queriesPerSecond: ((queries.length * 100) / ((endTime - startTime) / 1000)).toFixed(0)
        };
    },

    /**
     * اختبار أداء العرض
     */
    benchmarkRendering: function() {
        const startTime = performance.now();
        
        // محاكاة عرض مكونات متعددة
        for (let i = 0; i < 50; i++) {
            const element = document.createElement('div');
            element.innerHTML = `<div class="test-component">${i}</div>`;
            document.body.appendChild(element);
            document.body.removeChild(element);
        }
        
        const endTime = performance.now();
        
        return {
            renders: 50,
            duration: endTime - startTime,
            rendersPerSecond: (50 / ((endTime - startTime) / 1000)).toFixed(0)
        };
    },

    /**
     * اختبار استهلاك الذاكرة
     */
    benchmarkMemory: function() {
        if (!performance.memory) {
            return { message: 'معلومات الذاكرة غير متاحة' };
        }
        
        return {
            used: this.formatBytes(performance.memory.usedJSHeapSize),
            total: this.formatBytes(performance.memory.totalJSHeapSize),
            limit: this.formatBytes(performance.memory.jsHeapSizeLimit),
            usagePercentage: ((performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100).toFixed(2) + '%'
        };
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // تشغيل الاختبارات عند تحميل الصفحة
        if (this.config.enableAutoTest) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    this.runAllTests();
                }, 2000); // انتظار ثانيتين لضمان تحميل جميع الأنظمة
            });
        }
        
        // اختبار دوري للأداء
        if (this.config.enablePerformanceTest) {
            setInterval(() => {
                this.runPerformanceBenchmark();
            }, 5 * 60 * 1000); // كل 5 دقائق
        }
    },

    /**
     * تنسيق البايتات
     */
    formatBytes: function(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * الحصول على تقرير الجودة
     */
    getQualityReport: function() {
        return {
            timestamp: new Date().toISOString(),
            testResults: {
                total: this.state.totalTests,
                passed: this.state.passedTests,
                failed: this.state.failedTests,
                successRate: ((this.state.passedTests / this.state.totalTests) * 100).toFixed(1) + '%'
            },
            performance: this.runPerformanceBenchmark(),
            recommendations: this.getQualityRecommendations()
        };
    },

    /**
     * الحصول على توصيات الجودة
     */
    getQualityRecommendations: function() {
        const recommendations = [];
        
        // توصيات بناءً على نتائج الاختبار
        if (this.state.failedTests > 0) {
            recommendations.push({
                type: 'testing',
                message: `يوجد ${this.state.failedTests} اختبار فاشل يحتاج إصلاح`,
                priority: 'high'
            });
        }
        
        // توصيات الأداء
        if (performance.memory && performance.memory.usedJSHeapSize > 50 * 1024 * 1024) {
            recommendations.push({
                type: 'performance',
                message: 'استهلاك الذاكرة مرتفع - يُنصح بالتحسين',
                priority: 'medium'
            });
        }
        
        return recommendations;
    },

    /**
     * إرسال حدث
     */
    emit: function(eventName, data = null) {
        const event = new CustomEvent(eventName, {
            detail: data
        });
        document.dispatchEvent(event);
    }
};

// تصدير نظام الاختبار للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Testing;
}

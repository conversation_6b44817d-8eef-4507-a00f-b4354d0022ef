/**
 * ===================================
 * تصميم محرر القوالب المحاسبية
 * Template Editor Styles
 * ===================================
 */

/* ===== متغيرات محرر القوالب ===== */
:root {
    --editor-primary: #6366f1;
    --editor-secondary: #8b5cf6;
    --editor-success: #10b981;
    --editor-warning: #f59e0b;
    --editor-danger: #ef4444;
    --editor-info: #06b6d4;
    --editor-light: #f8fafc;
    --editor-dark: #1e293b;
    --editor-border: #e2e8f0;
    --editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --editor-radius: 0.5rem;
}

/* ===== حاوي المحرر الرئيسي ===== */
.template-editor-container {
    height: calc(100vh - 70px);
    display: flex;
    flex-direction: column;
    background: var(--editor-light);
}

/* ===== شريط الأدوات ===== */
.editor-toolbar {
    background: white;
    border-bottom: 2px solid var(--editor-border);
    padding: 1rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    box-shadow: var(--editor-shadow);
    z-index: 100;
}

.toolbar-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toolbar-section h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--editor-dark);
    margin: 0;
}

.toolbar-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.tool-btn {
    width: 40px;
    height: 40px;
    border: 2px solid var(--editor-border);
    background: white;
    border-radius: var(--editor-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--editor-dark);
}

.tool-btn:hover {
    border-color: var(--editor-primary);
    background: var(--editor-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--editor-shadow);
}

.tool-btn.active {
    border-color: var(--editor-primary);
    background: var(--editor-primary);
    color: white;
}

/* ===== منطقة العمل ===== */
.editor-workspace {
    flex: 1;
    display: flex;
    height: calc(100vh - 140px);
}

/* ===== لوحة الخصائص ===== */
.properties-panel {
    width: 300px;
    background: white;
    border-left: 2px solid var(--editor-border);
    padding: 1.5rem;
    overflow-y: auto;
}

.properties-panel h5 {
    color: var(--editor-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--editor-border);
}

.properties-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.property-group {
    background: var(--editor-light);
    padding: 1rem;
    border-radius: var(--editor-radius);
    border: 1px solid var(--editor-border);
}

.property-group label {
    font-weight: 600;
    color: var(--editor-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.property-group input,
.property-group select,
.property-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--editor-border);
    border-radius: var(--editor-radius);
    font-size: 0.875rem;
}

.property-group input:focus,
.property-group select:focus,
.property-group textarea:focus {
    outline: none;
    border-color: var(--editor-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== منطقة التصميم ===== */
.design-area {
    flex: 1;
    padding: 2rem;
    overflow: auto;
    background: #f1f5f9;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.template-canvas {
    width: 210mm; /* A4 width */
    min-height: 297mm; /* A4 height */
    background: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-radius: var(--editor-radius);
    padding: 20mm;
    margin: 2rem auto;
    position: relative;
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
}

/* ===== العناصر القابلة للتحرير ===== */
.editable-element {
    position: relative;
    margin-bottom: 1.5rem;
    padding: 0.5rem;
    border: 2px dashed transparent;
    border-radius: var(--editor-radius);
    transition: all 0.3s ease;
    cursor: pointer;
}

.editable-element:hover {
    border-color: var(--editor-primary);
    background: rgba(99, 102, 241, 0.05);
}

.editable-element.selected {
    border-color: var(--editor-primary);
    background: rgba(99, 102, 241, 0.1);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.editable-element::before {
    content: attr(data-type);
    position: absolute;
    top: -8px;
    right: 8px;
    background: var(--editor-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.editable-element:hover::before,
.editable-element.selected::before {
    opacity: 1;
}

/* ===== رأس القالب ===== */
.template-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid var(--editor-primary);
}

.company-logo {
    color: var(--editor-primary);
    text-align: center;
}

.company-info {
    flex: 1;
}

.company-name {
    color: var(--editor-dark);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.company-details {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0;
}

/* ===== عنوان المستند ===== */
.document-title {
    text-align: center;
    margin-bottom: 2rem;
}

.document-title h3 {
    color: var(--editor-dark);
    font-weight: 700;
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--editor-primary), var(--editor-secondary));
    color: white;
    border-radius: var(--editor-radius);
}

.document-number {
    font-weight: 600;
    color: var(--editor-dark);
}

/* ===== معلومات العميل ===== */
.customer-info {
    background: var(--editor-light);
    padding: 1.5rem;
    border-radius: var(--editor-radius);
    border: 1px solid var(--editor-border);
    margin-bottom: 2rem;
}

/* ===== تفاصيل المبلغ ===== */
.amount-details table {
    margin-bottom: 0;
}

.amount-details th {
    background: var(--editor-primary);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 1rem;
}

.amount-details td {
    padding: 1rem;
    text-align: center;
    font-weight: 500;
}

/* ===== المبلغ بالأحرف ===== */
.amount-words {
    background: #fef3c7;
    padding: 1rem;
    border-radius: var(--editor-radius);
    border: 2px solid #f59e0b;
    margin-bottom: 2rem;
    font-weight: 600;
}

/* ===== التوقيعات ===== */
.signatures {
    margin-top: 3rem;
}

.signature-box {
    padding: 1rem;
}

.signature-box p {
    font-weight: 600;
    margin-bottom: 2rem;
    color: var(--editor-dark);
}

.signature-line {
    height: 2px;
    background: var(--editor-dark);
    margin-top: 2rem;
}

/* ===== ذيل القالب ===== */
.template-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--editor-border);
}

/* ===== لوحة العناصر ===== */
.elements-panel {
    width: 250px;
    background: white;
    border-right: 2px solid var(--editor-border);
    padding: 1.5rem;
    overflow-y: auto;
}

.elements-panel h5 {
    color: var(--editor-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--editor-border);
}

.elements-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.element-item {
    padding: 0.75rem;
    background: var(--editor-light);
    border: 1px solid var(--editor-border);
    border-radius: var(--editor-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.element-item:hover {
    background: var(--editor-primary);
    color: white;
    transform: translateX(5px);
}

.element-item.active {
    background: var(--editor-primary);
    color: white;
    border-color: var(--editor-primary);
}

/* ===== حقول البيانات ===== */
.data-field {
    background: #e0f2fe;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px dashed #0891b2;
    color: #0891b2;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.data-field:hover {
    background: #0891b2;
    color: white;
}

/* ===== نافذة المعاينة ===== */
.preview-content {
    background: white;
    padding: 2rem;
    border-radius: var(--editor-radius);
    box-shadow: var(--editor-shadow);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 1200px) {
    .properties-panel {
        width: 250px;
    }
    
    .elements-panel {
        width: 200px;
    }
    
    .template-canvas {
        width: 180mm;
        min-height: 254mm;
        padding: 15mm;
    }
}

@media (max-width: 992px) {
    .editor-workspace {
        flex-direction: column;
        height: auto;
    }
    
    .properties-panel,
    .elements-panel {
        width: 100%;
        height: 200px;
        border: none;
        border-top: 2px solid var(--editor-border);
    }
    
    .design-area {
        order: -1;
        padding: 1rem;
    }
    
    .template-canvas {
        width: 100%;
        min-height: auto;
        padding: 1rem;
        margin: 0;
    }
}

@media (max-width: 768px) {
    .editor-toolbar {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .toolbar-section {
        min-width: 200px;
    }
    
    .template-canvas {
        padding: 0.5rem;
    }
    
    .template-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* ===== تأثيرات الطباعة ===== */
@media print {
    .editor-toolbar,
    .properties-panel,
    .elements-panel {
        display: none !important;
    }
    
    .design-area {
        padding: 0;
        background: white;
    }
    
    .template-canvas {
        box-shadow: none;
        border-radius: 0;
        margin: 0;
        width: 100%;
        min-height: auto;
    }
    
    .editable-element {
        border: none !important;
        background: transparent !important;
    }
    
    .editable-element::before {
        display: none !important;
    }
}

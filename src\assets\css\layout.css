/* ===================================
   Layout - التخطيط
   =================================== */

/* ===== التخطيط الأساسي ===== */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* ===== شريط التنقل ===== */
.navbar {
  background: var(--navbar-bg) !important;
  box-shadow: var(--shadow-md);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: var(--z-fixed);
  backdrop-filter: var(--backdrop-blur);
  height: var(--navbar-height);
}

.navbar .container-fluid {
  padding: 0 var(--spacing-lg);
}

/* العلامة التجارية */
.navbar-brand {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) 0;
  font-weight: var(--font-weight-bold);
  color: var(--white) !important;
  text-decoration: none;
  transition: var(--transition-all);
}

.navbar-brand:hover {
  color: var(--white) !important;
  transform: scale(1.05);
}

.brand-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.brand-icon {
  font-size: var(--font-size-2xl);
  color: var(--white);
}

.brand-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.brand-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

.brand-subtitle {
  font-size: var(--font-size-sm);
  opacity: var(--opacity-75);
}

/* قائمة التنقل */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius);
  transition: var(--transition-all);
  text-decoration: none;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white) !important;
  transform: translateY(-1px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white) !important;
  font-weight: var(--font-weight-semibold);
}

.nav-link i {
  font-size: var(--font-size-base);
}

.nav-text {
  font-size: var(--font-size-sm);
}

/* القوائم المنسدلة */
.dropdown-menu {
  background: var(--white);
  border: none;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  backdrop-filter: var(--backdrop-blur);
  min-width: 200px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-weight: var(--font-weight-normal);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-all);
  text-decoration: none;
  margin-bottom: var(--spacing-xs);
}

.dropdown-item:hover {
  background: var(--gradient-primary);
  color: var(--white);
  transform: translateX(4px);
}

.dropdown-item:last-child {
  margin-bottom: 0;
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color-light);
  margin: var(--spacing-sm) 0;
  border: none;
}

/* قائمة المستخدم */
.user-menu {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md) !important;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
  flex: 1;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--system-bg);
  min-height: calc(100vh - var(--navbar-height) - var(--footer-height));
}

/* ===== الحاوي ===== */
.container-fluid {
  width: 100%;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
  margin-left: auto;
  margin-right: auto;
}

.container {
  width: 100%;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
  margin-left: auto;
  margin-right: auto;
}

/* أحجام الحاويات */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

/* ===== الشبكة ===== */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(-0.5 * var(--spacing-md));
  margin-right: calc(-0.5 * var(--spacing-md));
}

.col,
[class*="col-"] {
  position: relative;
  width: 100%;
  padding-left: calc(0.5 * var(--spacing-md));
  padding-right: calc(0.5 * var(--spacing-md));
}

.col {
  flex: 1 0 0%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

/* أحجام الأعمدة */
.col-1 { flex: 0 0 auto; width: 8.33333333%; }
.col-2 { flex: 0 0 auto; width: 16.66666667%; }
.col-3 { flex: 0 0 auto; width: 25%; }
.col-4 { flex: 0 0 auto; width: 33.33333333%; }
.col-5 { flex: 0 0 auto; width: 41.66666667%; }
.col-6 { flex: 0 0 auto; width: 50%; }
.col-7 { flex: 0 0 auto; width: 58.33333333%; }
.col-8 { flex: 0 0 auto; width: 66.66666667%; }
.col-9 { flex: 0 0 auto; width: 75%; }
.col-10 { flex: 0 0 auto; width: 83.33333333%; }
.col-11 { flex: 0 0 auto; width: 91.66666667%; }
.col-12 { flex: 0 0 auto; width: 100%; }

/* ===== التذييل ===== */
.main-footer {
  background: var(--footer-bg);
  color: var(--white);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
  height: var(--footer-height);
  display: flex;
  align-items: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.footer-left p,
.footer-right p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* ===== الفجوات ===== */
.g-0 { gap: 0; }
.g-1 { gap: var(--spacing-xs); }
.g-2 { gap: var(--spacing-sm); }
.g-3 { gap: var(--spacing-md); }
.g-4 { gap: var(--spacing-lg); }
.g-5 { gap: var(--spacing-xl); }

/* ===== المسافات ===== */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* مسافات الاتجاهات */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ms-0 { margin-right: 0; }
.ms-1 { margin-right: var(--spacing-xs); }
.ms-2 { margin-right: var(--spacing-sm); }
.ms-3 { margin-right: var(--spacing-md); }
.ms-4 { margin-right: var(--spacing-lg); }
.ms-5 { margin-right: var(--spacing-xl); }

.me-0 { margin-left: 0; }
.me-1 { margin-left: var(--spacing-xs); }
.me-2 { margin-left: var(--spacing-sm); }
.me-3 { margin-left: var(--spacing-md); }
.me-4 { margin-left: var(--spacing-lg); }
.me-5 { margin-left: var(--spacing-xl); }

/* ===== العرض والارتفاع ===== */
.w-25 { width: 25%; }
.w-50 { width: 50%; }
.w-75 { width: 75%; }
.w-100 { width: 100%; }
.w-auto { width: auto; }

.h-25 { height: 25%; }
.h-50 { height: 50%; }
.h-75 { height: 75%; }
.h-100 { height: 100%; }
.h-auto { height: auto; }

/* ===== العرض والإخفاء ===== */
.d-none { display: none; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.d-grid { display: grid; }

/* ===== المحاذاة ===== */
.text-start { text-align: right; }
.text-end { text-align: left; }
.text-center { text-align: center; }

.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.justify-content-evenly { justify-content: space-evenly; }

.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

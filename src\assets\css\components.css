/* ===================================
   Components - المكونات
   =================================== */

/* ===== شاشة التحميل ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: var(--white);
}

.loading-logo {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s infinite;
}

.loading-text h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--white);
}

.loading-text p {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-xl);
}

.loading-spinner {
  display: flex;
  justify-content: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* ===== البطاقات ===== */
.card {
  background: var(--card-bg);
  border: var(--border-width) solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-all);
  overflow: hidden;
  backdrop-filter: var(--backdrop-blur);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-lg);
  border-bottom: none;
  font-weight: var(--font-weight-semibold);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  background: var(--bg-secondary);
  border-top: var(--border-width) solid var(--border-color-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* ===== الأزرار ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: var(--opacity-50);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* أنواع الأزرار */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--white);
}

.btn-success {
  background: var(--gradient-success);
  color: var(--white);
  border-color: var(--success-color);
}

.btn-success:hover {
  background: var(--success-dark);
  border-color: var(--success-dark);
  color: var(--white);
}

.btn-danger {
  background: var(--gradient-danger);
  color: var(--white);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background: var(--danger-dark);
  border-color: var(--danger-dark);
  color: var(--white);
}

.btn-warning {
  background: var(--gradient-warning);
  color: var(--white);
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background: var(--warning-dark);
  border-color: var(--warning-dark);
  color: var(--white);
}

.btn-info {
  background: var(--gradient-info);
  color: var(--white);
  border-color: var(--info-color);
}

.btn-info:hover {
  background: var(--info-dark);
  border-color: var(--info-dark);
  color: var(--white);
}

.btn-secondary {
  background: var(--gray-500);
  color: var(--white);
  border-color: var(--gray-500);
}

.btn-secondary:hover {
  background: var(--gray-600);
  border-color: var(--gray-600);
  color: var(--white);
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* الأزرار المحددة */
.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* ===== النماذج ===== */
.form-control,
.form-select {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-image: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition-all);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-label {
  display: inline-block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-text {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* ===== الشارات ===== */
.badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  color: var(--white);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-full);
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-danger {
  background-color: var(--danger-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

.badge-info {
  background-color: var(--info-color);
}

.badge-secondary {
  background-color: var(--gray-500);
}

/* ===== التنبيهات ===== */
.alert {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius);
}

.alert-primary {
  color: var(--primary-dark);
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.2);
}

.alert-success {
  color: var(--success-dark);
  background-color: rgba(var(--success-rgb), 0.1);
  border-color: rgba(var(--success-rgb), 0.2);
}

.alert-danger {
  color: var(--danger-dark);
  background-color: rgba(var(--danger-rgb), 0.1);
  border-color: rgba(var(--danger-rgb), 0.2);
}

.alert-warning {
  color: var(--warning-dark);
  background-color: rgba(var(--warning-rgb), 0.1);
  border-color: rgba(var(--warning-rgb), 0.2);
}

.alert-info {
  color: var(--info-dark);
  background-color: rgba(var(--info-rgb), 0.1);
  border-color: rgba(var(--info-rgb), 0.2);
}

/* ===== الجداول ===== */
.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-md);
  vertical-align: middle;
  border-bottom: var(--border-width) solid var(--border-color-light);
}

.table thead th {
  background: var(--gradient-primary);
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  border-bottom: 2px solid var(--primary-dark);
}

.table tbody tr {
  transition: var(--transition-all);
}

.table tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--bg-secondary);
}

/* ===== الإشعارات المنبثقة ===== */
.toast {
  position: fixed;
  top: var(--spacing-lg);
  left: var(--spacing-lg);
  min-width: 300px;
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  transform: translateX(-100%);
  transition: var(--transition-all);
}

.toast.show {
  transform: translateX(0);
}

.toast-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.toast-body {
  padding: var(--spacing-md);
}

/* ===== النوافذ المنبثقة ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-all);
  backdrop-filter: var(--backdrop-blur);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: var(--transition-all);
}

.modal.show .modal-dialog {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background: var(--gradient-primary);
  color: var(--white);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: var(--transition-all);
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: var(--border-width) solid var(--border-color-light);
}

/* ===== التبويبات ===== */
.nav-tabs {
  display: flex;
  border-bottom: 2px solid var(--border-color-light);
  margin-bottom: var(--spacing-lg);
}

.nav-tab {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
  border-bottom: 2px solid transparent;
  position: relative;
}

.nav-tab:hover {
  color: var(--primary-color);
  background: rgba(var(--primary-rgb), 0.05);
}

.nav-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ===== الأكورديون ===== */
.accordion {
  border: var(--border-width) solid var(--border-color-light);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.accordion-item {
  border-bottom: var(--border-width) solid var(--border-color-light);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  background: var(--bg-secondary);
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition-all);
}

.accordion-header:hover {
  background: rgba(var(--primary-rgb), 0.05);
}

.accordion-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.accordion-icon {
  transition: var(--transition-all);
  color: var(--text-secondary);
}

.accordion-item.active .accordion-icon {
  transform: rotate(180deg);
  color: var(--primary-color);
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.accordion-item.active .accordion-content {
  max-height: 500px;
}

.accordion-body {
  padding: var(--spacing-lg);
  background: var(--white);
}

/* ===== شريط التقدم ===== */
.progress {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  transition: width 0.6s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

.progress-bar.success {
  background: var(--gradient-success);
}

.progress-bar.warning {
  background: var(--gradient-warning);
}

.progress-bar.danger {
  background: var(--gradient-danger);
}

/* ===== البطاقات المتقدمة ===== */
.card-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card-advanced {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.stat-card-advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-primary);
}

.stat-card-advanced:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.stat-icon-advanced {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
  background: var(--gradient-primary);
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-label-advanced {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-top: var(--spacing-sm);
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

/* ===== قوائم البيانات ===== */
.data-list {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.data-list-header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.data-list-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.data-list-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.data-list-item {
  padding: var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color-light);
  transition: var(--transition-all);
  cursor: pointer;
}

.data-list-item:hover {
  background: rgba(var(--primary-rgb), 0.05);
}

.data-list-item:last-child {
  border-bottom: none;
}

.item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.item-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.item-details {
  flex: 1;
}

.item-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.item-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.item-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.item-action {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-all);
}

.item-action.primary {
  background: var(--primary-color);
  color: var(--white);
}

.item-action.secondary {
  background: var(--gray-200);
  color: var(--text-primary);
}

.item-action:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

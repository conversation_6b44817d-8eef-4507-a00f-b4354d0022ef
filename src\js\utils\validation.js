/**
 * ===================================
 * نظام التحقق من البيانات - Validation System
 * ===================================
 */

window.Validation = {
    // رسائل الخطأ الافتراضية
    messages: {
        required: 'هذا الحقل مطلوب',
        email: 'يرجى إدخال بريد إلكتروني صحيح',
        phone: 'يرجى إدخال رقم هاتف صحيح',
        saudiPhone: 'يرجى إدخال رقم جوال سعودي صحيح (05xxxxxxxx)',
        saudiId: 'يرجى إدخال رقم هوية سعودية صحيح',
        passport: 'يرجى إدخال رقم جواز صحيح',
        minLength: 'يجب أن يكون الحقل {min} أحرف على الأقل',
        maxLength: 'يجب أن لا يزيد الحقل عن {max} حرف',
        min: 'يجب أن تكون القيمة {min} على الأقل',
        max: 'يجب أن لا تزيد القيمة عن {max}',
        numeric: 'يجب أن يحتوي الحقل على أرقام فقط',
        alpha: 'يجب أن يحتوي الحقل على أحرف فقط',
        alphaNumeric: 'يجب أن يحتوي الحقل على أحرف وأرقام فقط',
        url: 'يرجى إدخال رابط صحيح',
        date: 'يرجى إدخال تاريخ صحيح',
        time: 'يرجى إدخال وقت صحيح',
        match: 'الحقلان غير متطابقان',
        unique: 'هذه القيمة مستخدمة بالفعل',
        fileSize: 'حجم الملف كبير جداً',
        fileType: 'نوع الملف غير مدعوم'
    },

    // قواعد التحقق
    rules: {
        /**
         * التحقق من الحقول المطلوبة
         */
        required: function(value) {
            if (value === null || value === undefined) return false;
            if (typeof value === 'string') return value.trim().length > 0;
            if (Array.isArray(value)) return value.length > 0;
            if (typeof value === 'object') return Object.keys(value).length > 0;
            return true;
        },

        /**
         * التحقق من البريد الإلكتروني
         */
        email: function(value) {
            if (!value) return true; // اختياري إذا لم يكن مطلوب
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(value);
        },

        /**
         * التحقق من رقم الهاتف العام
         */
        phone: function(value) {
            if (!value) return true;
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,15}$/;
            return phoneRegex.test(value.replace(/\s/g, ''));
        },

        /**
         * التحقق من رقم الجوال السعودي
         */
        saudiPhone: function(value) {
            if (!value) return true;
            const saudiPhoneRegex = /^(05|5)[0-9]{8}$/;
            return saudiPhoneRegex.test(value.replace(/[\s\-]/g, ''));
        },

        /**
         * التحقق من رقم الهوية السعودية
         */
        saudiId: function(value) {
            if (!value) return true;
            
            // التحقق من الطول والتنسيق
            if (!/^[12][0-9]{9}$/.test(value)) return false;
            
            // خوارزمية التحقق من رقم الهوية
            let sum = 0;
            for (let i = 0; i < 9; i++) {
                const digit = parseInt(value[i]);
                if (i % 2 === 0) {
                    const doubled = digit * 2;
                    sum += doubled > 9 ? doubled - 9 : doubled;
                } else {
                    sum += digit;
                }
            }
            
            const checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit === parseInt(value[9]);
        },

        /**
         * التحقق من رقم الجواز
         */
        passport: function(value) {
            if (!value) return true;
            // رقم الجواز يجب أن يكون 6-9 أحرف وأرقام
            const passportRegex = /^[A-Z0-9]{6,9}$/i;
            return passportRegex.test(value);
        },

        /**
         * التحقق من الحد الأدنى للطول
         */
        minLength: function(value, min) {
            if (!value) return true;
            return value.toString().length >= min;
        },

        /**
         * التحقق من الحد الأقصى للطول
         */
        maxLength: function(value, max) {
            if (!value) return true;
            return value.toString().length <= max;
        },

        /**
         * التحقق من الحد الأدنى للقيمة
         */
        min: function(value, min) {
            if (!value) return true;
            return parseFloat(value) >= min;
        },

        /**
         * التحقق من الحد الأقصى للقيمة
         */
        max: function(value, max) {
            if (!value) return true;
            return parseFloat(value) <= max;
        },

        /**
         * التحقق من الأرقام فقط
         */
        numeric: function(value) {
            if (!value) return true;
            return /^[0-9]+$/.test(value);
        },

        /**
         * التحقق من الأحرف فقط
         */
        alpha: function(value) {
            if (!value) return true;
            return /^[a-zA-Zأ-ي\s]+$/.test(value);
        },

        /**
         * التحقق من الأحرف والأرقام
         */
        alphaNumeric: function(value) {
            if (!value) return true;
            return /^[a-zA-Z0-9أ-ي\s]+$/.test(value);
        },

        /**
         * التحقق من الرابط
         */
        url: function(value) {
            if (!value) return true;
            try {
                new URL(value);
                return true;
            } catch {
                return false;
            }
        },

        /**
         * التحقق من التاريخ
         */
        date: function(value) {
            if (!value) return true;
            const date = new Date(value);
            return !isNaN(date.getTime());
        },

        /**
         * التحقق من الوقت
         */
        time: function(value) {
            if (!value) return true;
            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
            return timeRegex.test(value);
        },

        /**
         * التحقق من تطابق الحقول
         */
        match: function(value, matchValue) {
            return value === matchValue;
        },

        /**
         * التحقق من حجم الملف
         */
        fileSize: function(file, maxSize) {
            if (!file) return true;
            return file.size <= maxSize;
        },

        /**
         * التحقق من نوع الملف
         */
        fileType: function(file, allowedTypes) {
            if (!file) return true;
            return allowedTypes.includes(file.type) || 
                   allowedTypes.some(type => file.name.toLowerCase().endsWith(type));
        }
    },

    /**
     * التحقق من قيمة واحدة
     */
    validateValue: function(value, rules, fieldName = '') {
        const errors = [];

        for (const rule of rules) {
            let isValid = true;
            let message = '';

            if (typeof rule === 'string') {
                // قاعدة بسيطة
                if (this.rules[rule]) {
                    isValid = this.rules[rule](value);
                    message = this.messages[rule] || `خطأ في التحقق: ${rule}`;
                }
            } else if (typeof rule === 'object') {
                // قاعدة مع معاملات
                const ruleName = rule.rule;
                const params = rule.params || [];
                
                if (this.rules[ruleName]) {
                    isValid = this.rules[ruleName](value, ...params);
                    message = rule.message || this.messages[ruleName] || `خطأ في التحقق: ${ruleName}`;
                    
                    // استبدال المتغيرات في الرسالة
                    if (params.length > 0) {
                        message = message.replace(/\{(\w+)\}/g, (match, key) => {
                            const index = ['min', 'max', 'length'].indexOf(key);
                            return index !== -1 && params[index] !== undefined ? params[index] : match;
                        });
                    }
                }
            } else if (typeof rule === 'function') {
                // دالة مخصصة
                const result = rule(value);
                isValid = result === true;
                message = typeof result === 'string' ? result : 'قيمة غير صحيحة';
            }

            if (!isValid) {
                errors.push({
                    field: fieldName,
                    rule: typeof rule === 'string' ? rule : (rule.rule || 'custom'),
                    message: message
                });
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    /**
     * التحقق من نموذج كامل
     */
    validateForm: function(data, schema) {
        const results = {
            isValid: true,
            errors: [],
            fieldErrors: {}
        };

        for (const fieldName in schema) {
            const fieldRules = schema[fieldName];
            const fieldValue = data[fieldName];

            const validation = this.validateValue(fieldValue, fieldRules, fieldName);
            
            if (!validation.isValid) {
                results.isValid = false;
                results.errors.push(...validation.errors);
                results.fieldErrors[fieldName] = validation.errors;
            }
        }

        return results;
    },

    /**
     * التحقق من نموذج HTML
     */
    validateHTMLForm: function(formElement, schema) {
        const formData = new FormData(formElement);
        const data = {};

        // تحويل FormData إلى كائن
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }

        // إضافة القيم من العناصر غير المدرجة في FormData
        const inputs = formElement.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                data[input.name] = input.checked;
            } else if (input.type === 'radio') {
                if (input.checked) {
                    data[input.name] = input.value;
                }
            } else if (input.type === 'file') {
                data[input.name] = input.files[0] || null;
            }
        });

        return this.validateForm(data, schema);
    },

    /**
     * عرض أخطاء التحقق في النموذج
     */
    displayFormErrors: function(formElement, errors) {
        // مسح الأخطاء السابقة
        this.clearFormErrors(formElement);

        // عرض الأخطاء الجديدة
        for (const fieldName in errors) {
            const fieldErrors = errors[fieldName];
            const fieldElement = formElement.querySelector(`[name="${fieldName}"]`);

            if (fieldElement && fieldErrors.length > 0) {
                // إضافة فئة الخطأ
                fieldElement.classList.add('is-invalid');

                // إنشاء رسالة الخطأ
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = fieldErrors[0].message;

                // إدراج رسالة الخطأ
                fieldElement.parentNode.appendChild(errorDiv);
            }
        }
    },

    /**
     * مسح أخطاء النموذج
     */
    clearFormErrors: function(formElement) {
        // إزالة فئات الخطأ
        const invalidElements = formElement.querySelectorAll('.is-invalid');
        invalidElements.forEach(element => {
            element.classList.remove('is-invalid');
        });

        // إزالة رسائل الخطأ
        const errorMessages = formElement.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(message => {
            message.remove();
        });
    },

    /**
     * إضافة قاعدة تحقق مخصصة
     */
    addRule: function(name, validator, message) {
        this.rules[name] = validator;
        if (message) {
            this.messages[name] = message;
        }
    },

    /**
     * تحديث رسالة خطأ
     */
    setMessage: function(rule, message) {
        this.messages[rule] = message;
    },

    /**
     * التحقق السريع من البريد الإلكتروني
     */
    isValidEmail: function(email) {
        return this.rules.email(email);
    },

    /**
     * التحقق السريع من رقم الجوال السعودي
     */
    isValidSaudiPhone: function(phone) {
        return this.rules.saudiPhone(phone);
    },

    /**
     * التحقق السريع من رقم الهوية السعودية
     */
    isValidSaudiId: function(id) {
        return this.rules.saudiId(id);
    },

    /**
     * التحقق السريع من رقم الجواز
     */
    isValidPassport: function(passport) {
        return this.rules.passport(passport);
    }
};

// أمثلة على مخططات التحقق الشائعة
window.Validation.schemas = {
    // مخطط العميل
    customer: {
        name: ['required', { rule: 'minLength', params: [2] }],
        phone: ['required', 'saudiPhone'],
        email: ['email'],
        national_id: ['saudiId'],
        passport_number: ['passport']
    },

    // مخطط المورد
    supplier: {
        name: ['required', { rule: 'minLength', params: [2] }],
        supplier_type: ['required'],
        phone: ['phone'],
        email: ['email']
    },

    // مخطط الوكيل
    agent: {
        name: ['required', { rule: 'minLength', params: [2] }],
        phone: ['required', 'saudiPhone'],
        email: ['email'],
        commission_rate: ['required', { rule: 'min', params: [0] }, { rule: 'max', params: [100] }]
    },

    // مخطط تسجيل الدخول
    login: {
        username: ['required'],
        password: ['required', { rule: 'minLength', params: [6] }]
    },

    // مخطط تغيير كلمة المرور
    changePassword: {
        oldPassword: ['required'],
        newPassword: ['required', { rule: 'minLength', params: [8] }],
        confirmPassword: ['required']
    }
};

// تصدير نظام التحقق للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Validation;
}

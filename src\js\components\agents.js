/**
 * ===================================
 * مكون إدارة الوكلاء - Agents Component
 * ===================================
 */

window.Agents = {
    // بيانات الوكلاء
    data: {
        agents: [],
        filteredAgents: [],
        currentPage: 1,
        itemsPerPage: 10,
        totalPages: 0,
        filters: {
            name: '',
            status: '',
            city: ''
        },
        sortBy: 'created_at',
        sortOrder: 'desc'
    },

    /**
     * عرض صفحة الوكلاء
     */
    render: function(params = {}) {
        return `
            <div class="agents-container">
                <!-- عنوان الصفحة -->
                <div class="page-header-section">
                    <div class="page-title">
                        <div class="title-icon info">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="title-text">
                            <h2>إدارة الوكلاء</h2>
                            <p>إدارة الوكلاء والمندوبين والعمولات</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="action-button primary" onclick="Agents.showAddModal()">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة وكيل</span>
                        </button>
                        <button class="action-button secondary" onclick="Agents.exportData()">
                            <i class="fas fa-download"></i>
                            <span>تصدير</span>
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section">
                    <div class="stats-grid">
                        ${this.renderStats()}
                    </div>
                </div>

                <!-- الفلاتر -->
                <div class="filters-container">
                    <div class="filters-header">
                        <h3 class="filters-title">
                            <i class="fas fa-filter"></i>
                            البحث والفلترة
                        </h3>
                        <button class="filters-toggle" onclick="Agents.toggleFilters()">
                            <span>إظهار الفلاتر</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="filters-body" id="agents-filters">
                        ${this.renderFilters()}
                    </div>
                </div>

                <!-- جدول الوكلاء -->
                <div class="advanced-table-container">
                    <div class="table-toolbar">
                        <h3 class="table-title">قائمة الوكلاء</h3>
                        <div class="table-actions">
                            <div class="table-search">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" placeholder="البحث في الوكلاء..." 
                                       onkeyup="Agents.handleSearch(this.value)">
                            </div>
                        </div>
                    </div>
                    
                    <table class="advanced-table" id="agents-table">
                        <thead>
                            <tr>
                                <th class="sortable" onclick="Agents.sortBy('id')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    م
                                </th>
                                <th class="sortable" onclick="Agents.sortBy('agent_code')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    رقم الوكيل
                                </th>
                                <th class="sortable" onclick="Agents.sortBy('name')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    اسم الوكيل
                                </th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المدينة</th>
                                <th>نسبة العمولة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="agents-table-body">
                            ${this.renderTableRows()}
                        </tbody>
                    </table>

                    <!-- ترقيم الصفحات -->
                    <div class="table-pagination">
                        <div class="pagination-info">
                            عرض ${this.getPaginationInfo()}
                        </div>
                        <div class="pagination-controls">
                            ${this.renderPagination()}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل وكيل -->
            <div class="modal" id="agent-modal">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3 class="modal-title" id="agent-modal-title">إضافة وكيل جديد</h3>
                        <button class="modal-close" onclick="Agents.hideModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${this.renderAgentForm()}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="Agents.hideModal()">إلغاء</button>
                        <button class="btn btn-primary" onclick="Agents.saveAgent()">حفظ</button>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الإحصائيات
     */
    renderStats: function() {
        const stats = this.calculateStats();
        
        return `
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">إجمالي الوكلاء</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 8%
                    </div>
                </div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.active}</div>
                    <div class="stat-label">نشط</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 5%
                    </div>
                </div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.avgCommission}%</div>
                    <div class="stat-label">متوسط العمولة</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 2%
                    </div>
                </div>
            </div>
            
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.totalCommissions}</div>
                    <div class="stat-label">إجمالي العمولات</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 15%
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفلاتر
     */
    renderFilters: function() {
        return `
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">البحث بالاسم</label>
                    <input type="text" class="form-control" id="filter-name" 
                           placeholder="ابحث عن وكيل..." onkeyup="Agents.applyFilters()">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">حالة الوكيل</label>
                    <select class="form-select" id="filter-status" onchange="Agents.applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">المدينة</label>
                    <select class="form-select" id="filter-city" onchange="Agents.applyFilters()">
                        <option value="">جميع المدن</option>
                        ${this.getCitiesOptions()}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">نسبة العمولة</label>
                    <select class="form-select" id="filter-commission" onchange="Agents.applyFilters()">
                        <option value="">جميع النسب</option>
                        <option value="0-5">0% - 5%</option>
                        <option value="5-10">5% - 10%</option>
                        <option value="10-15">10% - 15%</option>
                        <option value="15+">أكثر من 15%</option>
                    </select>
                </div>
            </div>
            
            <div class="filters-actions">
                <button class="btn btn-primary" onclick="Agents.applyFilters()">
                    <i class="fas fa-filter"></i>
                    تطبيق الفلتر
                </button>
                <button class="btn btn-secondary" onclick="Agents.clearFilters()">
                    <i class="fas fa-eraser"></i>
                    مسح الفلتر
                </button>
            </div>
        `;
    },

    /**
     * عرض صفوف الجدول
     */
    renderTableRows: function() {
        if (this.data.filteredAgents.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وكلاء</p>
                    </td>
                </tr>
            `;
        }

        const startIndex = (this.data.currentPage - 1) * this.data.itemsPerPage;
        const endIndex = startIndex + this.data.itemsPerPage;
        const pageAgents = this.data.filteredAgents.slice(startIndex, endIndex);

        return pageAgents.map((agent, index) => `
            <tr>
                <td>${startIndex + index + 1}</td>
                <td><strong>${agent.agent_code || 'غير محدد'}</strong></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="item-avatar me-3">
                            ${agent.name ? agent.name.charAt(0) : 'و'}
                        </div>
                        <div>
                            <div class="fw-semibold">${agent.name || 'غير محدد'}</div>
                            <small class="text-muted">${agent.company || ''}</small>
                        </div>
                    </div>
                </td>
                <td>${agent.phone || 'غير محدد'}</td>
                <td>${agent.email || 'غير محدد'}</td>
                <td>${agent.city || 'غير محدد'}</td>
                <td>
                    <span class="badge badge-primary">
                        ${agent.commission_rate || 0}%
                    </span>
                </td>
                <td>
                    <span class="table-status ${this.getStatusClass(agent.status)}">
                        <span class="status-dot"></span>
                        ${this.getStatusName(agent.status)}
                    </span>
                </td>
                <td>
                    <div class="table-cell-actions">
                        <button class="cell-action view" onclick="Agents.viewAgent('${agent.id}')" 
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="cell-action edit" onclick="Agents.editAgent('${agent.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="cell-action delete" onclick="Agents.deleteAgent('${agent.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض نموذج الوكيل
     */
    renderAgentForm: function() {
        return `
            <form id="agent-form" class="advanced-form">
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-user form-section-icon"></i>
                        بيانات الوكيل
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label form-label-required">اسم الوكيل</label>
                            <input type="text" class="form-control" id="agent-name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="agent-company">
                        </div>
                        <div class="form-group">
                            <label class="form-label form-label-required">رقم الجوال</label>
                            <input type="tel" class="form-control" id="agent-phone" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="agent-email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهوية/الإقامة</label>
                            <input type="text" class="form-control" id="agent-national-id">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="agent-status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-map-marker-alt form-section-icon"></i>
                        معلومات الاتصال
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="agent-city">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="agent-country" value="السعودية">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="agent-address" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-percentage form-section-icon"></i>
                        إعدادات العمولة
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label form-label-required">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="agent-commission-rate" 
                                   min="0" max="100" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">نوع العمولة</label>
                            <select class="form-select" id="agent-commission-type">
                                <option value="percentage">نسبة مئوية</option>
                                <option value="fixed">مبلغ ثابت</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحد الأدنى للعمولة</label>
                            <input type="number" class="form-control" id="agent-min-commission" 
                                   min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحد الأقصى للعمولة</label>
                            <input type="number" class="form-control" id="agent-max-commission" 
                                   min="0" step="0.01">
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-cog form-section-icon"></i>
                        إعدادات إضافية
                    </h4>
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="agent-notes" rows="3"></textarea>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة مكون الوكلاء
     */
    init: function(params = {}) {
        console.log('🔄 تهيئة مكون الوكلاء');

        // تحميل البيانات
        this.loadData();

        // تطبيق الفلاتر
        this.applyFilters();

        console.log('✅ تم تهيئة مكون الوكلاء');
    },

    /**
     * تحميل بيانات الوكلاء
     */
    loadData: function() {
        if (window.Database) {
            this.data.agents = window.Database.findAll('agents');
        } else {
            this.data.agents = [];
        }

        this.data.filteredAgents = [...this.data.agents];
        this.calculatePagination();
    },

    /**
     * حساب الإحصائيات
     */
    calculateStats: function() {
        const total = this.data.agents.length;
        const active = this.data.agents.filter(a => a.status === 'active').length;

        const commissions = this.data.agents
            .filter(a => a.commission_rate)
            .map(a => parseFloat(a.commission_rate) || 0);

        const avgCommission = commissions.length > 0
            ? (commissions.reduce((sum, rate) => sum + rate, 0) / commissions.length).toFixed(1)
            : 0;

        const totalCommissions = '45,230 ر.س'; // يمكن حسابها من قاعدة البيانات

        return { total, active, avgCommission, totalCommissions };
    },

    /**
     * الحصول على خيارات المدن
     */
    getCitiesOptions: function() {
        const cities = [...new Set(this.data.agents.map(a => a.city).filter(Boolean))];
        return cities.map(city => `<option value="${city}">${city}</option>`).join('');
    },

    /**
     * الحصول على فئة الحالة
     */
    getStatusClass: function(status) {
        const statusMap = {
            'active': 'active',
            'inactive': 'inactive',
            'suspended': 'pending'
        };
        return statusMap[status] || 'inactive';
    },

    /**
     * الحصول على اسم الحالة
     */
    getStatusName: function(status) {
        const statusMap = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'معلق'
        };
        return statusMap[status] || 'غير محدد';
    },

    /**
     * عرض ترقيم الصفحات
     */
    renderPagination: function() {
        if (this.data.totalPages <= 1) return '';

        let pagination = '';

        // زر السابق
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === 1 ? 'disabled' : ''}"
                    onclick="Agents.goToPage(${this.data.currentPage - 1})"
                    ${this.data.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.data.totalPages; i++) {
            if (i === this.data.currentPage ||
                i === 1 ||
                i === this.data.totalPages ||
                (i >= this.data.currentPage - 1 && i <= this.data.currentPage + 1)) {

                pagination += `
                    <button class="pagination-btn ${i === this.data.currentPage ? 'active' : ''}"
                            onclick="Agents.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.data.currentPage - 2 || i === this.data.currentPage + 2) {
                pagination += '<span class="pagination-ellipsis">...</span>';
            }
        }

        // زر التالي
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}"
                    onclick="Agents.goToPage(${this.data.currentPage + 1})"
                    ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        return pagination;
    },

    /**
     * الحصول على معلومات الترقيم
     */
    getPaginationInfo: function() {
        const start = (this.data.currentPage - 1) * this.data.itemsPerPage + 1;
        const end = Math.min(start + this.data.itemsPerPage - 1, this.data.filteredAgents.length);
        const total = this.data.filteredAgents.length;

        return `${start} - ${end} من ${total} وكيل`;
    },

    /**
     * حساب الترقيم
     */
    calculatePagination: function() {
        this.data.totalPages = Math.ceil(this.data.filteredAgents.length / this.data.itemsPerPage);
        if (this.data.currentPage > this.data.totalPages) {
            this.data.currentPage = 1;
        }
    },

    /**
     * الانتقال لصفحة معينة
     */
    goToPage: function(page) {
        if (page >= 1 && page <= this.data.totalPages) {
            this.data.currentPage = page;
            this.updateTable();
        }
    },

    /**
     * تبديل عرض الفلاتر
     */
    toggleFilters: function() {
        const filtersBody = document.getElementById('agents-filters');
        const toggleBtn = document.querySelector('.filters-toggle');

        if (filtersBody.classList.contains('show')) {
            filtersBody.classList.remove('show');
            toggleBtn.innerHTML = '<span>إظهار الفلاتر</span><i class="fas fa-chevron-down"></i>';
        } else {
            filtersBody.classList.add('show');
            toggleBtn.innerHTML = '<span>إخفاء الفلاتر</span><i class="fas fa-chevron-up"></i>';
        }
    },

    /**
     * تطبيق الفلاتر
     */
    applyFilters: function() {
        // الحصول على قيم الفلاتر
        const nameFilter = document.getElementById('filter-name')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('filter-status')?.value || '';
        const cityFilter = document.getElementById('filter-city')?.value || '';
        const commissionFilter = document.getElementById('filter-commission')?.value || '';

        // تطبيق الفلاتر
        this.data.filteredAgents = this.data.agents.filter(agent => {
            const matchesName = !nameFilter ||
                agent.name?.toLowerCase().includes(nameFilter) ||
                agent.agent_code?.toLowerCase().includes(nameFilter);

            const matchesStatus = !statusFilter || agent.status === statusFilter;
            const matchesCity = !cityFilter || agent.city === cityFilter;

            let matchesCommission = true;
            if (commissionFilter) {
                const rate = parseFloat(agent.commission_rate) || 0;
                switch (commissionFilter) {
                    case '0-5':
                        matchesCommission = rate >= 0 && rate <= 5;
                        break;
                    case '5-10':
                        matchesCommission = rate > 5 && rate <= 10;
                        break;
                    case '10-15':
                        matchesCommission = rate > 10 && rate <= 15;
                        break;
                    case '15+':
                        matchesCommission = rate > 15;
                        break;
                }
            }

            return matchesName && matchesStatus && matchesCity && matchesCommission;
        });

        // إعادة حساب الترقيم
        this.data.currentPage = 1;
        this.calculatePagination();

        // تحديث الجدول
        this.updateTable();
    },

    /**
     * مسح الفلاتر
     */
    clearFilters: function() {
        // مسح قيم الفلاتر
        const filterInputs = document.querySelectorAll('#agents-filters input, #agents-filters select');
        filterInputs.forEach(input => {
            input.value = '';
        });

        // إعادة تطبيق الفلاتر
        this.applyFilters();
    },

    /**
     * البحث في الوكلاء
     */
    handleSearch: function(searchTerm) {
        const nameFilter = document.getElementById('filter-name');
        if (nameFilter) {
            nameFilter.value = searchTerm;
            this.applyFilters();
        }
    },

    /**
     * ترتيب البيانات
     */
    sortBy: function(field) {
        if (this.data.sortBy === field) {
            this.data.sortOrder = this.data.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.data.sortBy = field;
            this.data.sortOrder = 'asc';
        }

        this.data.filteredAgents.sort((a, b) => {
            let aValue = a[field] || '';
            let bValue = b[field] || '';

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.data.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.updateTable();
        this.updateSortIcons(field);
    },

    /**
     * تحديث أيقونات الترتيب
     */
    updateSortIcons: function(activeField) {
        const sortIcons = document.querySelectorAll('.sort-icon');
        sortIcons.forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
            icon.parentElement.classList.remove('sorted');
        });

        const activeIcon = document.querySelector(`th[onclick="Agents.sortBy('${activeField}')"] .sort-icon`);
        if (activeIcon) {
            activeIcon.className = `fas fa-sort-${this.data.sortOrder === 'asc' ? 'up' : 'down'} sort-icon`;
            activeIcon.parentElement.classList.add('sorted');
        }
    },

    /**
     * تحديث الجدول
     */
    updateTable: function() {
        const tableBody = document.getElementById('agents-table-body');
        const paginationInfo = document.querySelector('.pagination-info');
        const paginationControls = document.querySelector('.pagination-controls');

        if (tableBody) {
            tableBody.innerHTML = this.renderTableRows();
        }

        if (paginationInfo) {
            paginationInfo.innerHTML = `عرض ${this.getPaginationInfo()}`;
        }

        if (paginationControls) {
            paginationControls.innerHTML = this.renderPagination();
        }
    },

    /**
     * عرض نافذة إضافة وكيل
     */
    showAddModal: function() {
        const modal = document.getElementById('agent-modal');
        const title = document.getElementById('agent-modal-title');

        title.textContent = 'إضافة وكيل جديد';
        this.clearForm();
        modal.classList.add('show');
    },

    /**
     * عرض نافذة تعديل وكيل
     */
    editAgent: function(agentId) {
        const agent = this.data.agents.find(a => a.id === agentId);
        if (!agent) return;

        const modal = document.getElementById('agent-modal');
        const title = document.getElementById('agent-modal-title');

        title.textContent = 'تعديل بيانات الوكيل';
        this.fillForm(agent);
        modal.classList.add('show');
    },

    /**
     * عرض تفاصيل الوكيل
     */
    viewAgent: function(agentId) {
        const agent = this.data.agents.find(a => a.id === agentId);
        if (!agent) return;

        // يمكن إضافة نافذة عرض التفاصيل هنا
        console.log('عرض تفاصيل الوكيل:', agent);
    },

    /**
     * حذف وكيل
     */
    deleteAgent: function(agentId) {
        if (confirm('هل أنت متأكد من حذف هذا الوكيل؟')) {
            if (window.Database) {
                window.Database.delete('agents', agentId);
                this.loadData();
                this.applyFilters();

                if (window.Notifications) {
                    window.Notifications.success('تم حذف الوكيل بنجاح');
                }
            }
        }
    },

    /**
     * إخفاء النافذة المنبثقة
     */
    hideModal: function() {
        const modal = document.getElementById('agent-modal');
        modal.classList.remove('show');
    },

    /**
     * حفظ بيانات الوكيل
     */
    saveAgent: function() {
        const form = document.getElementById('agent-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const agentData = this.getFormData();

        if (window.Database) {
            // إضافة رقم وكيل تلقائي
            if (!agentData.agent_code) {
                agentData.agent_code = this.generateAgentCode();
            }

            window.Database.insert('agents', agentData);
            this.loadData();
            this.applyFilters();
            this.hideModal();

            if (window.Notifications) {
                window.Notifications.success('تم حفظ بيانات الوكيل بنجاح');
            }
        }
    },

    /**
     * الحصول على بيانات النموذج
     */
    getFormData: function() {
        return {
            name: document.getElementById('agent-name')?.value || '',
            company: document.getElementById('agent-company')?.value || '',
            phone: document.getElementById('agent-phone')?.value || '',
            email: document.getElementById('agent-email')?.value || '',
            national_id: document.getElementById('agent-national-id')?.value || '',
            status: document.getElementById('agent-status')?.value || 'active',
            city: document.getElementById('agent-city')?.value || '',
            country: document.getElementById('agent-country')?.value || 'السعودية',
            address: document.getElementById('agent-address')?.value || '',
            commission_rate: parseFloat(document.getElementById('agent-commission-rate')?.value) || 0,
            commission_type: document.getElementById('agent-commission-type')?.value || 'percentage',
            min_commission: parseFloat(document.getElementById('agent-min-commission')?.value) || 0,
            max_commission: parseFloat(document.getElementById('agent-max-commission')?.value) || 0,
            notes: document.getElementById('agent-notes')?.value || ''
        };
    },

    /**
     * ملء النموذج ببيانات الوكيل
     */
    fillForm: function(agent) {
        document.getElementById('agent-name').value = agent.name || '';
        document.getElementById('agent-company').value = agent.company || '';
        document.getElementById('agent-phone').value = agent.phone || '';
        document.getElementById('agent-email').value = agent.email || '';
        document.getElementById('agent-national-id').value = agent.national_id || '';
        document.getElementById('agent-status').value = agent.status || 'active';
        document.getElementById('agent-city').value = agent.city || '';
        document.getElementById('agent-country').value = agent.country || 'السعودية';
        document.getElementById('agent-address').value = agent.address || '';
        document.getElementById('agent-commission-rate').value = agent.commission_rate || 0;
        document.getElementById('agent-commission-type').value = agent.commission_type || 'percentage';
        document.getElementById('agent-min-commission').value = agent.min_commission || 0;
        document.getElementById('agent-max-commission').value = agent.max_commission || 0;
        document.getElementById('agent-notes').value = agent.notes || '';
    },

    /**
     * مسح النموذج
     */
    clearForm: function() {
        const form = document.getElementById('agent-form');
        if (form) {
            form.reset();
        }
    },

    /**
     * إنشاء رقم وكيل
     */
    generateAgentCode: function() {
        const prefix = 'AGT';
        const timestamp = Date.now().toString().slice(-6);
        return `${prefix}${timestamp}`;
    },

    /**
     * تصدير البيانات
     */
    exportData: function() {
        if (window.Helpers) {
            const headers = ['رقم الوكيل', 'اسم الوكيل', 'الشركة', 'الجوال', 'البريد الإلكتروني', 'المدينة', 'نسبة العمولة', 'الحالة'];
            const data = this.data.filteredAgents.map(agent => [
                agent.agent_code || '',
                agent.name || '',
                agent.company || '',
                agent.phone || '',
                agent.email || '',
                agent.city || '',
                `${agent.commission_rate || 0}%`,
                this.getStatusName(agent.status)
            ]);

            window.Helpers.downloadCSV(data, 'agents.csv', headers);

            if (window.Notifications) {
                window.Notifications.success('تم تصدير بيانات الوكلاء بنجاح');
            }
        }
    }
};

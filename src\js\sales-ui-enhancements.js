/**
 * تحسينات واجهة المستخدم لنظام المبيعات
 * يتضمن تأثيرات بصرية وتفاعلية متقدمة
 */

// تهيئة التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initUIEnhancements();
});

/**
 * تهيئة جميع تحسينات واجهة المستخدم
 */
function initUIEnhancements() {
    console.log('🎨 تهيئة تحسينات واجهة المستخدم...');
    
    // تطبيق التأثيرات
    applyAnimations();
    setupInteractiveElements();
    initTooltips();
    setupLoadingEffects();
    initCounterAnimations();
    setupParticleEffects();
    
    console.log('✅ تم تطبيق تحسينات واجهة المستخدم');
}

/**
 * تطبيق الرسوم المتحركة
 */
function applyAnimations() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in');
    });
    
    // تأثير الانزلاق للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * إعداد العناصر التفاعلية
 */
function setupInteractiveElements() {
    // تأثير النقر على البطاقات
    const clickableCards = document.querySelectorAll('.card[data-clickable]');
    clickableCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // تأثير التمرير على الجداول
    const tableRows = document.querySelectorAll('table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transform = 'translateX(5px)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * تهيئة التلميحات
 */
function initTooltips() {
    // إضافة تلميحات للأزرار
    const buttons = document.querySelectorAll('.btn[title]');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, e.target.getAttribute('title'));
        });
        
        button.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

/**
 * إظهار التلميح
 */
function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 9999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);
}

/**
 * إخفاء التلميح
 */
function hideTooltip() {
    const tooltip = document.querySelector('.custom-tooltip');
    if (tooltip) {
        tooltip.style.opacity = '0';
        setTimeout(() => {
            tooltip.remove();
        }, 300);
    }
}

/**
 * إعداد تأثيرات التحميل
 */
function setupLoadingEffects() {
    // تأثير التحميل للأزرار
    const loadingButtons = document.querySelectorAll('.btn[data-loading]');
    loadingButtons.forEach(button => {
        button.addEventListener('click', function() {
            showButtonLoading(this);
        });
    });
}

/**
 * إظهار تأثير التحميل للزر
 */
function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
    button.disabled = true;
    
    // محاكاة التحميل
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}

/**
 * تهيئة رسوم العدادات المتحركة
 */
function initCounterAnimations() {
    const counters = document.querySelectorAll('.counter');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 ثانية
        const step = target / (duration / 16); // 60 FPS
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString('ar-SA');
        }, 16);
    };
    
    // مراقب التقاطع لتشغيل الرسوم المتحركة عند الظهور
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
}

/**
 * إعداد تأثيرات الجسيمات
 */
function setupParticleEffects() {
    // تأثير الجسيمات عند النقر
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-primary')) {
            createParticleEffect(e.clientX, e.clientY);
        }
    });
}

/**
 * إنشاء تأثير الجسيمات
 */
function createParticleEffect(x, y) {
    for (let i = 0; i < 6; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            width: 6px;
            height: 6px;
            background: #007bff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            left: ${x}px;
            top: ${y}px;
        `;
        
        document.body.appendChild(particle);
        
        // رسم متحرك للجسيمة
        const angle = (i / 6) * Math.PI * 2;
        const velocity = 100;
        const vx = Math.cos(angle) * velocity;
        const vy = Math.sin(angle) * velocity;
        
        let currentX = x;
        let currentY = y;
        let opacity = 1;
        
        const animate = () => {
            currentX += vx * 0.02;
            currentY += vy * 0.02;
            opacity -= 0.02;
            
            particle.style.left = currentX + 'px';
            particle.style.top = currentY + 'px';
            particle.style.opacity = opacity;
            
            if (opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                particle.remove();
            }
        };
        
        requestAnimationFrame(animate);
    }
}

/**
 * تأثير الموجة عند النقر
 */
function createRippleEffect(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * تأثير التمرير السلس
 */
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

/**
 * تأثير الاهتزاز للتحذيرات
 */
function shakeElement(element) {
    element.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        element.style.animation = '';
    }, 500);
}

/**
 * تأثير النبض للعناصر المهمة
 */
function pulseElement(element) {
    element.style.animation = 'pulse 1s ease-in-out infinite';
}

/**
 * إيقاف تأثير النبض
 */
function stopPulse(element) {
    element.style.animation = '';
}

/**
 * تأثير التدرج اللوني المتحرك
 */
function animateGradient(element) {
    element.style.background = 'linear-gradient(-45deg, #667eea, #764ba2, #667eea, #764ba2)';
    element.style.backgroundSize = '400% 400%';
    element.style.animation = 'gradientShift 3s ease infinite';
}

/**
 * إضافة الأنماط المطلوبة للرسوم المتحركة
 */
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out forwards;
        }
        
        .animate-bounce {
            animation: bounce 1s ease-in-out;
        }
    `;
    document.head.appendChild(style);
}

// تطبيق الأنماط عند التحميل
addAnimationStyles();

// تصدير الوظائف للاستخدام العام
window.UIEnhancements = {
    showButtonLoading,
    createRippleEffect,
    smoothScrollTo,
    shakeElement,
    pulseElement,
    stopPulse,
    animateGradient,
    createParticleEffect
};

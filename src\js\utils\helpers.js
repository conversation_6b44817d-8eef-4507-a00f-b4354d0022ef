/**
 * ===================================
 * المساعدات والأدوات - Helper Functions
 * ===================================
 */

window.Helpers = {
    /**
     * تنسيق التاريخ
     */
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD-MM-YYYY':
                return `${day}-${month}-${year}`;
            case 'YYYY-MM-DD HH:mm':
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            case 'DD/MM/YYYY HH:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'HH:mm':
                return `${hours}:${minutes}`;
            case 'relative':
                return this.getRelativeTime(d);
            default:
                return d.toLocaleDateString('ar-SA');
        }
    },

    /**
     * الحصول على الوقت النسبي
     */
    getRelativeTime: function(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        const months = Math.floor(days / 30);
        const years = Math.floor(months / 12);

        if (years > 0) return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;
        if (months > 0) return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;
        if (days > 0) return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
        if (hours > 0) return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
        if (minutes > 0) return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
        return 'منذ لحظات';
    },

    /**
     * تنسيق الأرقام
     */
    formatNumber: function(number, decimals = 0, thousandsSeparator = ',') {
        if (isNaN(number)) return '0';
        
        const num = parseFloat(number);
        const formatted = num.toFixed(decimals);
        
        if (thousandsSeparator) {
            const parts = formatted.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
            return parts.join('.');
        }
        
        return formatted;
    },

    /**
     * تنسيق العملة
     */
    formatCurrency: function(amount, currency = 'SAR', decimals = 2) {
        const formatted = this.formatNumber(amount, decimals);
        
        const currencySymbols = {
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€',
            'GBP': '£'
        };
        
        const symbol = currencySymbols[currency] || currency;
        return `${formatted} ${symbol}`;
    },

    /**
     * تنظيف النص
     */
    sanitizeText: function(text) {
        if (!text) return '';
        
        return text
            .replace(/[<>]/g, '') // إزالة علامات HTML الأساسية
            .replace(/javascript:/gi, '') // إزالة JavaScript
            .trim();
    },

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * التحقق من صحة رقم الهاتف السعودي
     */
    isValidSaudiPhone: function(phone) {
        const phoneRegex = /^(05|5)[0-9]{8}$/;
        return phoneRegex.test(phone.replace(/[\s-]/g, ''));
    },

    /**
     * التحقق من صحة رقم الهوية السعودية
     */
    isValidSaudiID: function(id) {
        if (!id || id.length !== 10) return false;
        
        const idRegex = /^[12][0-9]{9}$/;
        if (!idRegex.test(id)) return false;
        
        // خوارزمية التحقق من رقم الهوية
        let sum = 0;
        for (let i = 0; i < 9; i++) {
            const digit = parseInt(id[i]);
            if (i % 2 === 0) {
                const doubled = digit * 2;
                sum += doubled > 9 ? doubled - 9 : doubled;
            } else {
                sum += digit;
            }
        }
        
        const checkDigit = (10 - (sum % 10)) % 10;
        return checkDigit === parseInt(id[9]);
    },

    /**
     * إنشاء معرف فريد
     */
    generateUUID: function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    /**
     * إنشاء رقم مرجعي
     */
    generateReference: function(prefix = '', length = 8) {
        const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let result = prefix;
        
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return result;
    },

    /**
     * تحويل النص إلى slug
     */
    slugify: function(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]+/g, '')
            .replace(/\-\-+/g, '-')
            .replace(/^-+/, '')
            .replace(/-+$/, '');
    },

    /**
     * اقتطاع النص
     */
    truncateText: function(text, maxLength = 100, suffix = '...') {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength - suffix.length) + suffix;
    },

    /**
     * تحويل الحجم بالبايت إلى وحدة قابلة للقراءة
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * نسخ النص إلى الحافظة
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text);
        } else {
            // طريقة بديلة للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return Promise.resolve();
        }
    },

    /**
     * تحميل ملف
     */
    downloadFile: function(content, filename, contentType = 'text/plain') {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    },

    /**
     * تحميل JSON كملف
     */
    downloadJSON: function(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        this.downloadFile(jsonString, filename, 'application/json');
    },

    /**
     * تحميل CSV كملف
     */
    downloadCSV: function(data, filename, headers = []) {
        let csvContent = '';
        
        // إضافة العناوين
        if (headers.length > 0) {
            csvContent += headers.join(',') + '\n';
        }
        
        // إضافة البيانات
        data.forEach(row => {
            const values = Array.isArray(row) ? row : Object.values(row);
            csvContent += values.map(value => `"${value}"`).join(',') + '\n';
        });
        
        this.downloadFile(csvContent, filename, 'text/csv');
    },

    /**
     * قراءة ملف
     */
    readFile: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                resolve(e.target.result);
            };
            
            reader.onerror = function() {
                reject(new Error('فشل في قراءة الملف'));
            };
            
            reader.readAsText(file);
        });
    },

    /**
     * التحقق من نوع الملف
     */
    isValidFileType: function(file, allowedTypes) {
        if (!file || !allowedTypes) return false;
        
        const fileType = file.type;
        const fileName = file.name.toLowerCase();
        
        return allowedTypes.some(type => {
            if (type.startsWith('.')) {
                return fileName.endsWith(type);
            } else {
                return fileType === type || fileType.startsWith(type + '/');
            }
        });
    },

    /**
     * تأخير التنفيذ
     */
    delay: function(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * تجميع الاستدعاءات (Debounce)
     */
    debounce: function(func, wait, immediate = false) {
        let timeout;
        
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            
            if (callNow) func.apply(this, args);
        };
    },

    /**
     * تحديد معدل الاستدعاءات (Throttle)
     */
    throttle: function(func, limit) {
        let inThrottle;
        
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * التحقق من كون القيمة فارغة
     */
    isEmpty: function(value) {
        if (value == null) return true;
        if (typeof value === 'string') return value.trim() === '';
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    },

    /**
     * دمج الكائنات بعمق
     */
    deepMerge: function(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    },

    /**
     * استخراج قيمة من كائن باستخدام مسار
     */
    getNestedValue: function(obj, path, defaultValue = null) {
        const keys = path.split('.');
        let current = obj;
        
        for (const key of keys) {
            if (current == null || typeof current !== 'object') {
                return defaultValue;
            }
            current = current[key];
        }
        
        return current !== undefined ? current : defaultValue;
    },

    /**
     * تعيين قيمة في كائن باستخدام مسار
     */
    setNestedValue: function(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let current = obj;
        
        for (const key of keys) {
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[lastKey] = value;
        return obj;
    }
};

// تصدير المساعدات للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Helpers;
}

/**
 * ===================================
 * تصميم نظام إدارة القوالب المحاسبية
 * Templates Management System Styles
 * ===================================
 */

/* ===== متغيرات القوالب ===== */
:root {
    --templates-primary: #6366f1;
    --templates-secondary: #8b5cf6;
    --templates-success: #10b981;
    --templates-warning: #f59e0b;
    --templates-danger: #ef4444;
    --templates-info: #06b6d4;
    --templates-light: #f8fafc;
    --templates-dark: #1e293b;
    
    /* تدرجات القوالب */
    --templates-gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --templates-gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --templates-gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --templates-gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    
    /* ظلال القوالب */
    --templates-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --templates-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --templates-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --templates-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* نصف أقطار القوالب */
    --templates-radius-sm: 0.375rem;
    --templates-radius-md: 0.5rem;
    --templates-radius-lg: 0.75rem;
    --templates-radius-xl: 1rem;
    --templates-radius-2xl: 1.5rem;
}

/* ===== حاوي النظام الرئيسي ===== */
.templates-dashboard {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.templates-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== رأس النظام ===== */
.templates-header {
    background: var(--templates-gradient-primary);
    color: white;
    padding: 3rem 2rem;
    border-radius: var(--templates-radius-2xl);
    margin-bottom: 3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    box-shadow: var(--templates-shadow-xl);
}

.templates-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 2;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
}

.header-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-text p {
    font-size: 1.1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 1rem;
    z-index: 2;
}

.header-actions .btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--templates-radius-lg);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-actions .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.header-actions .btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.header-actions .btn-outline-secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.header-actions .btn-outline-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* ===== إحصائيات سريعة ===== */
.templates-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: var(--templates-radius-xl);
    box-shadow: var(--templates-shadow-md);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--templates-primary);
    transition: width 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--templates-shadow-lg);
}

.stat-card:hover::before {
    width: 8px;
}

.stat-card.stat-primary::before { background: var(--templates-primary); }
.stat-card.stat-success::before { background: var(--templates-success); }
.stat-card.stat-info::before { background: var(--templates-info); }
.stat-card.stat-warning::before { background: var(--templates-warning); }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-primary .stat-icon { background: var(--templates-gradient-primary); }
.stat-success .stat-icon { background: var(--templates-gradient-success); }
.stat-info .stat-icon { background: var(--templates-gradient-info); }
.stat-warning .stat-icon { background: var(--templates-gradient-warning); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--templates-dark);
}

.stat-content p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0.25rem 0 0 0;
}

/* ===== أدوات البحث والفلترة ===== */
.templates-filters {
    background: white;
    padding: 1.5rem;
    border-radius: var(--templates-radius-xl);
    box-shadow: var(--templates-shadow-md);
    margin-bottom: 2rem;
}

.filters-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    z-index: 2;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e2e8f0;
    border-radius: var(--templates-radius-lg);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--templates-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-group select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: var(--templates-radius-lg);
    font-size: 1rem;
    background: white;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--templates-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== فئات القوالب ===== */
.templates-categories {
    margin-bottom: 2rem;
}

.category-tabs {
    display: flex;
    gap: 0.5rem;
    background: white;
    padding: 0.5rem;
    border-radius: var(--templates-radius-xl);
    box-shadow: var(--templates-shadow-md);
    overflow-x: auto;
}

.category-tab {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: #64748b;
    border-radius: var(--templates-radius-lg);
    font-weight: 600;
    transition: all 0.3s ease;
    white-space: nowrap;
    cursor: pointer;
}

.category-tab:hover {
    background: #f1f5f9;
    color: var(--templates-primary);
}

.category-tab.active {
    background: var(--templates-gradient-primary);
    color: white;
    box-shadow: var(--templates-shadow-md);
}

/* ===== شبكة القوالب ===== */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.template-card {
    background: white;
    border-radius: var(--templates-radius-xl);
    box-shadow: var(--templates-shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--templates-shadow-xl);
}

.template-preview {
    height: 200px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--templates-primary);
    position: relative;
    overflow: hidden;
}

.template-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.template-card:hover .template-preview::before {
    transform: translateX(100%);
}

.template-info {
    padding: 1.5rem;
}

.template-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--templates-dark);
    margin: 0 0 0.5rem 0;
}

.template-description {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.template-category {
    background: var(--templates-gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--templates-radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.template-status {
    padding: 0.25rem 0.75rem;
    border-radius: var(--templates-radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.template-status.active {
    background: #dcfce7;
    color: #166534;
}

.template-status.draft {
    background: #fef3c7;
    color: #92400e;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

.template-actions .btn {
    flex: 1;
    padding: 0.5rem;
    border-radius: var(--templates-radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* ===== أدوات سريعة ===== */
.quick-tools {
    background: white;
    padding: 2rem;
    border-radius: var(--templates-radius-xl);
    box-shadow: var(--templates-shadow-md);
}

.quick-tools h3 {
    color: var(--templates-dark);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: var(--templates-radius-lg);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.tool-card:hover {
    background: white;
    border-color: var(--templates-primary);
    transform: translateY(-3px);
    box-shadow: var(--templates-shadow-lg);
}

.tool-card i {
    font-size: 2.5rem;
    color: var(--templates-primary);
    margin-bottom: 1rem;
}

.tool-card h4 {
    color: var(--templates-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tool-card p {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0;
}

/* ===== تأثيرات الحركة ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.counting-animation {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .templates-header {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .header-text h1 {
        font-size: 2rem;
    }
    
    .templates-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .category-tabs {
        justify-content: flex-start;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .templates-container {
        padding: 0 0.5rem;
    }
    
    .templates-header {
        padding: 2rem 1rem;
        margin-bottom: 2rem;
    }
    
    .header-text h1 {
        font-size: 1.75rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .templates-filters {
        padding: 1rem;
    }
    
    .quick-tools {
        padding: 1.5rem;
    }
}

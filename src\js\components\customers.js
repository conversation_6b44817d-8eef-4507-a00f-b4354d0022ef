/**
 * ===================================
 * مكون إدارة العملاء - Customers Component
 * ===================================
 */

window.Customers = {
    // بيانات العملاء
    data: {
        customers: [],
        filteredCustomers: [],
        currentPage: 1,
        itemsPerPage: 10,
        totalPages: 0,
        filters: {
            name: '',
            agent: '',
            status: '',
            type: ''
        },
        sortBy: 'created_at',
        sortOrder: 'desc'
    },

    /**
     * عرض صفحة العملاء
     */
    render: function(params = {}) {
        return `
            <div class="customers-container">
                <!-- عنوان الصفحة -->
                <div class="page-header-section">
                    <div class="page-title">
                        <div class="title-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="title-text">
                            <h2>إدارة العملاء</h2>
                            <p>إدارة بيانات العملاء والمعاملات والتأشيرات</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="action-button primary" onclick="Customers.showAddModal()">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة عميل</span>
                        </button>
                        <button class="action-button secondary" onclick="Customers.exportData()">
                            <i class="fas fa-download"></i>
                            <span>تصدير</span>
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="stats-section">
                    <div class="stats-grid">
                        ${this.renderStats()}
                    </div>
                </div>

                <!-- الفلاتر -->
                <div class="filters-container">
                    <div class="filters-header">
                        <h3 class="filters-title">
                            <i class="fas fa-filter"></i>
                            البحث والفلترة
                        </h3>
                        <button class="filters-toggle" onclick="Customers.toggleFilters()">
                            <span>إظهار الفلاتر</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="filters-body" id="customers-filters">
                        ${this.renderFilters()}
                    </div>
                </div>

                <!-- جدول العملاء -->
                <div class="advanced-table-container">
                    <div class="table-toolbar">
                        <h3 class="table-title">قائمة العملاء</h3>
                        <div class="table-actions">
                            <div class="table-search">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" placeholder="البحث في العملاء..." 
                                       onkeyup="Customers.handleSearch(this.value)">
                            </div>
                        </div>
                    </div>
                    
                    <table class="advanced-table" id="customers-table">
                        <thead>
                            <tr>
                                <th class="sortable" onclick="Customers.sortBy('id')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    م
                                </th>
                                <th class="sortable" onclick="Customers.sortBy('customer_code')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    رقم العميل
                                </th>
                                <th class="sortable" onclick="Customers.sortBy('name')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    الاسم
                                </th>
                                <th class="sortable" onclick="Customers.sortBy('phone')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    الجوال
                                </th>
                                <th class="sortable" onclick="Customers.sortBy('passport_number')">
                                    <i class="fas fa-sort sort-icon"></i>
                                    رقم الجواز
                                </th>
                                <th>الوكيل</th>
                                <th>نوع التأشيرة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                            ${this.renderTableRows()}
                        </tbody>
                    </table>

                    <!-- ترقيم الصفحات -->
                    <div class="table-pagination">
                        <div class="pagination-info">
                            عرض ${this.getPaginationInfo()}
                        </div>
                        <div class="pagination-controls">
                            ${this.renderPagination()}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل عميل -->
            <div class="modal" id="customer-modal">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3 class="modal-title" id="customer-modal-title">إضافة عميل جديد</h3>
                        <button class="modal-close" onclick="Customers.hideModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${this.renderCustomerForm()}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="Customers.hideModal()">إلغاء</button>
                        <button class="btn btn-primary" onclick="Customers.saveCustomer()">حفظ</button>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الإحصائيات
     */
    renderStats: function() {
        const stats = this.calculateStats();
        
        return `
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">إجمالي العملاء</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 8%
                    </div>
                </div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.pending}</div>
                    <div class="stat-label">قيد المعاملة</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 12%
                    </div>
                </div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.completed}</div>
                    <div class="stat-label">مكتملة</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 5%
                    </div>
                </div>
            </div>
            
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${stats.newToday}</div>
                    <div class="stat-label">جديد اليوم</div>
                    <div class="stat-trend up">
                        <i class="fas fa-arrow-up"></i> 15%
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض الفلاتر
     */
    renderFilters: function() {
        return `
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">البحث بالاسم</label>
                    <input type="text" class="form-control" id="filter-name" 
                           placeholder="ابحث عن عميل..." onkeyup="Customers.applyFilters()">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">الوكيل</label>
                    <select class="form-select" id="filter-agent" onchange="Customers.applyFilters()">
                        <option value="">جميع الوكلاء</option>
                        ${this.getAgentsOptions()}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">حالة المعاملة</label>
                    <select class="form-select" id="filter-status" onchange="Customers.applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="قيد المعاملة">قيد المعاملة</option>
                        <option value="مسلم للمكتب">مسلم للمكتب</option>
                        <option value="قيد الترحيل للسفارة">قيد الترحيل للسفارة</option>
                        <option value="مؤشر في السفارة">مؤشر في السفارة</option>
                        <option value="مؤشر في المكتب">مؤشر في المكتب</option>
                        <option value="مسلم للعميل مؤشر">مسلم للعميل مؤشر</option>
                        <option value="مسلم للعميل غير مؤشر">مسلم للعميل غير مؤشر</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">نوع العميل</label>
                    <select class="form-select" id="filter-type" onchange="Customers.applyFilters()">
                        <option value="">جميع الأنواع</option>
                        <option value="individual">فرد</option>
                        <option value="company">شركة</option>
                        <option value="agent">وكيل</option>
                    </select>
                </div>
            </div>
            
            <div class="filters-actions">
                <button class="btn btn-primary" onclick="Customers.applyFilters()">
                    <i class="fas fa-filter"></i>
                    تطبيق الفلتر
                </button>
                <button class="btn btn-secondary" onclick="Customers.clearFilters()">
                    <i class="fas fa-eraser"></i>
                    مسح الفلتر
                </button>
            </div>
        `;
    },

    /**
     * عرض صفوف الجدول
     */
    renderTableRows: function() {
        if (this.data.filteredCustomers.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عملاء</p>
                    </td>
                </tr>
            `;
        }

        const startIndex = (this.data.currentPage - 1) * this.data.itemsPerPage;
        const endIndex = startIndex + this.data.itemsPerPage;
        const pageCustomers = this.data.filteredCustomers.slice(startIndex, endIndex);

        return pageCustomers.map((customer, index) => `
            <tr>
                <td>${startIndex + index + 1}</td>
                <td><strong>${customer.customer_code || 'غير محدد'}</strong></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="item-avatar me-3">
                            ${customer.name ? customer.name.charAt(0) : 'ع'}
                        </div>
                        <div>
                            <div class="fw-semibold">${customer.name || 'غير محدد'}</div>
                            <small class="text-muted">${customer.email || ''}</small>
                        </div>
                    </div>
                </td>
                <td>${customer.phone || 'غير محدد'}</td>
                <td>${customer.passport_number || 'غير محدد'}</td>
                <td>${customer.agent_name || 'غير محدد'}</td>
                <td>${customer.visa_type || 'غير محدد'}</td>
                <td>
                    <span class="table-status ${this.getStatusClass(customer.status)}">
                        <span class="status-dot"></span>
                        ${customer.status || 'غير محدد'}
                    </span>
                </td>
                <td>
                    <div class="table-cell-actions">
                        <button class="cell-action view" onclick="Customers.viewCustomer('${customer.id}')" 
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="cell-action edit" onclick="Customers.editCustomer('${customer.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="cell-action delete" onclick="Customers.deleteCustomer('${customer.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض نموذج العميل
     */
    renderCustomerForm: function() {
        return `
            <form id="customer-form" class="advanced-form">
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-user form-section-icon"></i>
                        البيانات الشخصية
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label form-label-required">الاسم الكامل</label>
                            <input type="text" class="form-control" id="customer-name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهوية/الإقامة</label>
                            <input type="text" class="form-control" id="customer-national-id">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الجواز</label>
                            <input type="text" class="form-control" id="customer-passport">
                        </div>
                        <div class="form-group">
                            <label class="form-label form-label-required">رقم الجوال</label>
                            <input type="tel" class="form-control" id="customer-phone" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customer-email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">نوع العميل</label>
                            <select class="form-select" id="customer-type">
                                <option value="individual">فرد</option>
                                <option value="company">شركة</option>
                                <option value="agent">وكيل</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-map-marker-alt form-section-icon"></i>
                        معلومات الاتصال
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="customer-address" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="customer-city">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الدولة</label>
                            <select class="form-select" id="customer-country">
                                <option value="السعودية">السعودية</option>
                                <option value="الإمارات">الإمارات</option>
                                <option value="الكويت">الكويت</option>
                                <option value="قطر">قطر</option>
                                <option value="البحرين">البحرين</option>
                                <option value="عمان">عمان</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-cog form-section-icon"></i>
                        إعدادات إضافية
                    </h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">الوكيل المسؤول</label>
                            <select class="form-select" id="customer-agent">
                                <option value="">اختر الوكيل</option>
                                ${this.getAgentsOptions()}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">حد الائتمان</label>
                            <input type="number" class="form-control" id="customer-credit-limit" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="customer-notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        `;
    },

    /**
     * تهيئة مكون العملاء
     */
    init: function(params = {}) {
        console.log('🔄 تهيئة مكون العملاء');
        
        // تحميل البيانات
        this.loadData();
        
        // تطبيق الفلاتر
        this.applyFilters();
        
        console.log('✅ تم تهيئة مكون العملاء');
    },

    /**
     * تحميل بيانات العملاء
     */
    loadData: function() {
        if (window.Database) {
            this.data.customers = window.Database.findAll('customers');
        } else {
            this.data.customers = [];
        }
        
        this.data.filteredCustomers = [...this.data.customers];
        this.calculatePagination();
    },

    /**
     * حساب الإحصائيات
     */
    calculateStats: function() {
        const total = this.data.customers.length;
        const pending = this.data.customers.filter(c => c.status === 'قيد المعاملة').length;
        const completed = this.data.customers.filter(c => c.status === 'مسلم للعميل مؤشر').length;
        
        const today = new Date().toISOString().split('T')[0];
        const newToday = this.data.customers.filter(c => 
            c.created_at && c.created_at.startsWith(today)
        ).length;
        
        return { total, pending, completed, newToday };
    },

    /**
     * الحصول على خيارات الوكلاء
     */
    getAgentsOptions: function() {
        const agents = window.Database ? window.Database.findAll('agents') : [];
        return agents.map(agent => 
            `<option value="${agent.id}">${agent.name}</option>`
        ).join('');
    },

    /**
     * الحصول على فئة الحالة
     */
    getStatusClass: function(status) {
        const statusMap = {
            'قيد المعاملة': 'pending',
            'مسلم للمكتب': 'processing',
            'قيد الترحيل للسفارة': 'processing',
            'مؤشر في السفارة': 'processing',
            'مؤشر في المكتب': 'processing',
            'مسلم للعميل مؤشر': 'active',
            'مسلم للعميل غير مؤشر': 'inactive'
        };
        
        return statusMap[status] || 'pending';
    },

    /**
     * عرض ترقيم الصفحات
     */
    renderPagination: function() {
        if (this.data.totalPages <= 1) return '';

        let pagination = '';

        // زر السابق
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === 1 ? 'disabled' : ''}"
                    onclick="Customers.goToPage(${this.data.currentPage - 1})"
                    ${this.data.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.data.totalPages; i++) {
            if (i === this.data.currentPage ||
                i === 1 ||
                i === this.data.totalPages ||
                (i >= this.data.currentPage - 1 && i <= this.data.currentPage + 1)) {

                pagination += `
                    <button class="pagination-btn ${i === this.data.currentPage ? 'active' : ''}"
                            onclick="Customers.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.data.currentPage - 2 || i === this.data.currentPage + 2) {
                pagination += '<span class="pagination-ellipsis">...</span>';
            }
        }

        // زر التالي
        pagination += `
            <button class="pagination-btn ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}"
                    onclick="Customers.goToPage(${this.data.currentPage + 1})"
                    ${this.data.currentPage === this.data.totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;

        return pagination;
    },

    /**
     * الحصول على معلومات الترقيم
     */
    getPaginationInfo: function() {
        const start = (this.data.currentPage - 1) * this.data.itemsPerPage + 1;
        const end = Math.min(start + this.data.itemsPerPage - 1, this.data.filteredCustomers.length);
        const total = this.data.filteredCustomers.length;

        return `${start} - ${end} من ${total} عميل`;
    },

    /**
     * حساب الترقيم
     */
    calculatePagination: function() {
        this.data.totalPages = Math.ceil(this.data.filteredCustomers.length / this.data.itemsPerPage);
        if (this.data.currentPage > this.data.totalPages) {
            this.data.currentPage = 1;
        }
    },

    /**
     * الانتقال لصفحة معينة
     */
    goToPage: function(page) {
        if (page >= 1 && page <= this.data.totalPages) {
            this.data.currentPage = page;
            this.updateTable();
        }
    },

    /**
     * تبديل عرض الفلاتر
     */
    toggleFilters: function() {
        const filtersBody = document.getElementById('customers-filters');
        const toggleBtn = document.querySelector('.filters-toggle');

        if (filtersBody.classList.contains('show')) {
            filtersBody.classList.remove('show');
            toggleBtn.innerHTML = '<span>إظهار الفلاتر</span><i class="fas fa-chevron-down"></i>';
        } else {
            filtersBody.classList.add('show');
            toggleBtn.innerHTML = '<span>إخفاء الفلاتر</span><i class="fas fa-chevron-up"></i>';
        }
    },

    /**
     * تطبيق الفلاتر
     */
    applyFilters: function() {
        // الحصول على قيم الفلاتر
        const nameFilter = document.getElementById('filter-name')?.value.toLowerCase() || '';
        const agentFilter = document.getElementById('filter-agent')?.value || '';
        const statusFilter = document.getElementById('filter-status')?.value || '';
        const typeFilter = document.getElementById('filter-type')?.value || '';

        // تطبيق الفلاتر
        this.data.filteredCustomers = this.data.customers.filter(customer => {
            const matchesName = !nameFilter ||
                customer.name?.toLowerCase().includes(nameFilter) ||
                customer.customer_code?.toLowerCase().includes(nameFilter);

            const matchesAgent = !agentFilter || customer.agent_id === agentFilter;
            const matchesStatus = !statusFilter || customer.status === statusFilter;
            const matchesType = !typeFilter || customer.customer_type === typeFilter;

            return matchesName && matchesAgent && matchesStatus && matchesType;
        });

        // إعادة حساب الترقيم
        this.data.currentPage = 1;
        this.calculatePagination();

        // تحديث الجدول
        this.updateTable();
    },

    /**
     * مسح الفلاتر
     */
    clearFilters: function() {
        // مسح قيم الفلاتر
        const filterInputs = document.querySelectorAll('#customers-filters input, #customers-filters select');
        filterInputs.forEach(input => {
            input.value = '';
        });

        // إعادة تطبيق الفلاتر
        this.applyFilters();
    },

    /**
     * البحث في العملاء
     */
    handleSearch: function(searchTerm) {
        const nameFilter = document.getElementById('filter-name');
        if (nameFilter) {
            nameFilter.value = searchTerm;
            this.applyFilters();
        }
    },

    /**
     * ترتيب البيانات
     */
    sortBy: function(field) {
        if (this.data.sortBy === field) {
            this.data.sortOrder = this.data.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.data.sortBy = field;
            this.data.sortOrder = 'asc';
        }

        this.data.filteredCustomers.sort((a, b) => {
            let aValue = a[field] || '';
            let bValue = b[field] || '';

            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.data.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.updateTable();
        this.updateSortIcons(field);
    },

    /**
     * تحديث أيقونات الترتيب
     */
    updateSortIcons: function(activeField) {
        const sortIcons = document.querySelectorAll('.sort-icon');
        sortIcons.forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
            icon.parentElement.classList.remove('sorted');
        });

        const activeIcon = document.querySelector(`th[onclick="Customers.sortBy('${activeField}')"] .sort-icon`);
        if (activeIcon) {
            activeIcon.className = `fas fa-sort-${this.data.sortOrder === 'asc' ? 'up' : 'down'} sort-icon`;
            activeIcon.parentElement.classList.add('sorted');
        }
    },

    /**
     * تحديث الجدول
     */
    updateTable: function() {
        const tableBody = document.getElementById('customers-table-body');
        const paginationInfo = document.querySelector('.pagination-info');
        const paginationControls = document.querySelector('.pagination-controls');

        if (tableBody) {
            tableBody.innerHTML = this.renderTableRows();
        }

        if (paginationInfo) {
            paginationInfo.innerHTML = `عرض ${this.getPaginationInfo()}`;
        }

        if (paginationControls) {
            paginationControls.innerHTML = this.renderPagination();
        }
    },

    /**
     * عرض نافذة إضافة عميل
     */
    showAddModal: function() {
        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('customer-modal-title');

        title.textContent = 'إضافة عميل جديد';
        this.clearForm();
        modal.classList.add('show');
    },

    /**
     * عرض نافذة تعديل عميل
     */
    editCustomer: function(customerId) {
        const customer = this.data.customers.find(c => c.id === customerId);
        if (!customer) return;

        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('customer-modal-title');

        title.textContent = 'تعديل بيانات العميل';
        this.fillForm(customer);
        modal.classList.add('show');
    },

    /**
     * عرض تفاصيل العميل
     */
    viewCustomer: function(customerId) {
        const customer = this.data.customers.find(c => c.id === customerId);
        if (!customer) return;

        // يمكن إضافة نافذة عرض التفاصيل هنا
        console.log('عرض تفاصيل العميل:', customer);
    },

    /**
     * حذف عميل
     */
    deleteCustomer: function(customerId) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            if (window.Database) {
                window.Database.delete('customers', customerId);
                this.loadData();
                this.applyFilters();

                if (window.Notifications) {
                    window.Notifications.success('تم حذف العميل بنجاح');
                }
            }
        }
    },

    /**
     * إخفاء النافذة المنبثقة
     */
    hideModal: function() {
        const modal = document.getElementById('customer-modal');
        modal.classList.remove('show');
    },

    /**
     * حفظ بيانات العميل
     */
    saveCustomer: function() {
        const form = document.getElementById('customer-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const customerData = this.getFormData();

        if (window.Database) {
            // إضافة رقم عميل تلقائي
            if (!customerData.customer_code) {
                customerData.customer_code = this.generateCustomerCode();
            }

            window.Database.insert('customers', customerData);
            this.loadData();
            this.applyFilters();
            this.hideModal();

            if (window.Notifications) {
                window.Notifications.success('تم حفظ بيانات العميل بنجاح');
            }
        }
    },

    /**
     * الحصول على بيانات النموذج
     */
    getFormData: function() {
        return {
            name: document.getElementById('customer-name')?.value || '',
            national_id: document.getElementById('customer-national-id')?.value || '',
            passport_number: document.getElementById('customer-passport')?.value || '',
            phone: document.getElementById('customer-phone')?.value || '',
            email: document.getElementById('customer-email')?.value || '',
            customer_type: document.getElementById('customer-type')?.value || 'individual',
            address: document.getElementById('customer-address')?.value || '',
            city: document.getElementById('customer-city')?.value || '',
            country: document.getElementById('customer-country')?.value || 'السعودية',
            agent_id: document.getElementById('customer-agent')?.value || null,
            credit_limit: parseFloat(document.getElementById('customer-credit-limit')?.value) || 0,
            notes: document.getElementById('customer-notes')?.value || '',
            status: 'قيد المعاملة'
        };
    },

    /**
     * ملء النموذج ببيانات العميل
     */
    fillForm: function(customer) {
        document.getElementById('customer-name').value = customer.name || '';
        document.getElementById('customer-national-id').value = customer.national_id || '';
        document.getElementById('customer-passport').value = customer.passport_number || '';
        document.getElementById('customer-phone').value = customer.phone || '';
        document.getElementById('customer-email').value = customer.email || '';
        document.getElementById('customer-type').value = customer.customer_type || 'individual';
        document.getElementById('customer-address').value = customer.address || '';
        document.getElementById('customer-city').value = customer.city || '';
        document.getElementById('customer-country').value = customer.country || 'السعودية';
        document.getElementById('customer-agent').value = customer.agent_id || '';
        document.getElementById('customer-credit-limit').value = customer.credit_limit || 0;
        document.getElementById('customer-notes').value = customer.notes || '';
    },

    /**
     * مسح النموذج
     */
    clearForm: function() {
        const form = document.getElementById('customer-form');
        if (form) {
            form.reset();
        }
    },

    /**
     * إنشاء رقم عميل
     */
    generateCustomerCode: function() {
        const prefix = 'CUS';
        const timestamp = Date.now().toString().slice(-6);
        return `${prefix}${timestamp}`;
    },

    /**
     * تصدير البيانات
     */
    exportData: function() {
        if (window.Helpers) {
            const headers = ['رقم العميل', 'الاسم', 'الجوال', 'البريد الإلكتروني', 'رقم الجواز', 'الحالة'];
            const data = this.data.filteredCustomers.map(customer => [
                customer.customer_code || '',
                customer.name || '',
                customer.phone || '',
                customer.email || '',
                customer.passport_number || '',
                customer.status || ''
            ]);

            window.Helpers.downloadCSV(data, 'customers.csv', headers);

            if (window.Notifications) {
                window.Notifications.success('تم تصدير بيانات العملاء بنجاح');
            }
        }
    }
};

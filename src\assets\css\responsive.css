/* ===================================
   Responsive Design - التصميم المتجاوب
   =================================== */

/* ===== الشاشات الكبيرة جداً (2XL) ===== */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
  
  .modules-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* ===== الشاشات الكبيرة (XL) ===== */
@media (max-width: 1399.98px) {
  .modules-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ===== الشاشات المتوسطة الكبيرة (LG) ===== */
@media (max-width: 1199.98px) {
  .navbar-nav .nav-text {
    display: none;
  }
  
  .navbar-nav .nav-link {
    padding: var(--spacing-sm) !important;
    justify-content: center;
  }
  
  .brand-text {
    display: none;
  }
  
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .page-header-section {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }
  
  .page-actions {
    justify-content: center;
  }
}

/* ===== الشاشات المتوسطة (MD) ===== */
@media (max-width: 991.98px) {
  .main-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }
  
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .module-card {
    padding: var(--spacing-lg);
  }
  
  .module-icon {
    width: 60px;
    height: 60px;
    font-size: var(--font-size-2xl);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: var(--spacing-lg);
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-xl);
  }
  
  .page-header h1 {
    font-size: var(--font-size-3xl);
  }
  
  .page-header .lead {
    font-size: var(--font-size-lg);
  }
  
  .title-text h2 {
    font-size: var(--font-size-2xl);
  }
  
  .title-icon {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-xl);
  }
}

/* ===== الشاشات الصغيرة المتوسطة (SM) ===== */
@media (max-width: 767.98px) {
  .navbar {
    height: auto;
    min-height: var(--navbar-height);
  }
  
  .navbar .container-fluid {
    padding: var(--spacing-md);
  }
  
  .navbar-brand {
    font-size: var(--font-size-lg);
  }
  
  .brand-icon {
    font-size: var(--font-size-xl);
  }
  
  .main-content {
    padding: var(--spacing-md);
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .module-card {
    padding: var(--spacing-xl);
  }
  
  .quick-actions {
    padding: var(--spacing-lg);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .quick-action-btn {
    padding: var(--spacing-lg);
  }
  
  .quick-action-icon {
    font-size: var(--font-size-2xl);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .activity-section {
    padding: var(--spacing-lg);
  }
  
  .page-header {
    margin-bottom: var(--spacing-xl);
  }
  
  .page-header h1 {
    font-size: var(--font-size-2xl);
  }
  
  .page-header .lead {
    font-size: var(--font-size-base);
  }
  
  .page-header-section {
    padding: var(--spacing-lg);
  }
  
  .page-title {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .page-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .action-button {
    justify-content: center;
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}

/* ===== الشاشات الصغيرة جداً (XS) ===== */
@media (max-width: 575.98px) {
  .container-fluid {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
  
  .main-content {
    padding: var(--spacing-sm);
  }
  
  .module-card {
    padding: var(--spacing-lg);
  }
  
  .module-icon {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-xl);
  }
  
  .module-title {
    font-size: var(--font-size-lg);
  }
  
  .quick-actions {
    padding: var(--spacing-md);
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-action-btn {
    padding: var(--spacing-md);
  }
  
  .activity-section {
    padding: var(--spacing-md);
  }
  
  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
  
  .page-header-section {
    padding: var(--spacing-md);
  }
  
  .title-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
  }
  
  .title-text h2 {
    font-size: var(--font-size-xl);
  }
  
  .stat-number {
    font-size: var(--font-size-2xl);
  }
}

/* ===== الشاشات الصغيرة جداً (أقل من 400px) ===== */
@media (max-width: 399.98px) {
  .page-header h1 {
    font-size: var(--font-size-xl);
  }
  
  .module-title {
    font-size: var(--font-size-base);
  }
  
  .module-description {
    font-size: var(--font-size-sm);
  }
  
  .quick-actions-title,
  .activity-title {
    font-size: var(--font-size-lg);
  }
  
  .stat-number {
    font-size: var(--font-size-xl);
  }
}

/* ===== التوجه الأفقي للأجهزة المحمولة ===== */
@media (max-width: 767.98px) and (orientation: landscape) {
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== إخفاء العناصر حسب حجم الشاشة ===== */
.d-sm-none {
  display: none !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-flex {
    display: flex !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
}

/* ===== الطباعة ===== */
@media print {
  .navbar,
  .main-footer,
  .no-print {
    display: none !important;
  }
  
  .main-content {
    padding: 0;
    background: white;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .modules-grid,
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .module-card,
  .stat-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

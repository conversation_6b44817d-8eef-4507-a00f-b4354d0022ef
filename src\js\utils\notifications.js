/**
 * ===================================
 * نظام الإشعارات - Notifications System
 * ===================================
 */

window.Notifications = {
    // إعدادات الإشعارات
    config: {
        position: 'top-left', // top-left, top-right, bottom-left, bottom-right
        duration: 5000, // مدة العرض بالميلي ثانية
        maxNotifications: 5, // أقصى عدد إشعارات
        animation: 'slide', // slide, fade, bounce
        rtl: true // دعم RTL
    },

    // قائمة الإشعارات النشطة
    activeNotifications: [],

    // عداد الإشعارات
    notificationCounter: 0,

    /**
     * تهيئة نظام الإشعارات
     */
    init: function() {
        this.createContainer();
        this.addStyles();
        console.log('✅ تم تهيئة نظام الإشعارات');
    },

    /**
     * إنشاء حاوي الإشعارات
     */
    createContainer: function() {
        if (document.getElementById('notifications-container')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = `notifications-container ${this.config.position}`;
        
        document.body.appendChild(container);
    },

    /**
     * إضافة الأنماط
     */
    addStyles: function() {
        if (document.getElementById('notifications-styles')) {
            return;
        }

        const styles = document.createElement('style');
        styles.id = 'notifications-styles';
        styles.textContent = `
            .notifications-container {
                position: fixed;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
                width: 100%;
            }
            
            .notifications-container.top-left {
                top: 20px;
                left: 20px;
            }
            
            .notifications-container.top-right {
                top: 20px;
                right: 20px;
            }
            
            .notifications-container.bottom-left {
                bottom: 20px;
                left: 20px;
            }
            
            .notifications-container.bottom-right {
                bottom: 20px;
                right: 20px;
            }
            
            .notification {
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
                margin-bottom: 12px;
                padding: 16px 20px;
                pointer-events: auto;
                position: relative;
                overflow: hidden;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
            }
            
            .notification:hover {
                transform: translateY(-2px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            }
            
            .notification::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: var(--primary-color, #667eea);
            }
            
            .notification.success::before {
                background: var(--success-color, #28a745);
            }
            
            .notification.error::before {
                background: var(--danger-color, #dc3545);
            }
            
            .notification.warning::before {
                background: var(--warning-color, #ffc107);
            }
            
            .notification.info::before {
                background: var(--info-color, #17a2b8);
            }
            
            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .notification-title {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                font-size: 14px;
                color: var(--text-primary, #333);
            }
            
            .notification-icon {
                font-size: 16px;
            }
            
            .notification.success .notification-icon {
                color: var(--success-color, #28a745);
            }
            
            .notification.error .notification-icon {
                color: var(--danger-color, #dc3545);
            }
            
            .notification.warning .notification-icon {
                color: var(--warning-color, #ffc107);
            }
            
            .notification.info .notification-icon {
                color: var(--info-color, #17a2b8);
            }
            
            .notification-close {
                background: none;
                border: none;
                color: var(--text-muted, #6c757d);
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
            }
            
            .notification-close:hover {
                background: rgba(0, 0, 0, 0.1);
                color: var(--text-primary, #333);
            }
            
            .notification-message {
                font-size: 13px;
                line-height: 1.4;
                color: var(--text-secondary, #6c757d);
                margin: 0;
            }
            
            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0, 0, 0, 0.1);
                transition: width linear;
            }
            
            .notification.success .notification-progress {
                background: var(--success-color, #28a745);
            }
            
            .notification.error .notification-progress {
                background: var(--danger-color, #dc3545);
            }
            
            .notification.warning .notification-progress {
                background: var(--warning-color, #ffc107);
            }
            
            .notification.info .notification-progress {
                background: var(--info-color, #17a2b8);
            }
            
            /* الحركات */
            .notification.slide-enter {
                transform: translateX(-100%);
                opacity: 0;
            }
            
            .notification.slide-enter-active {
                transform: translateX(0);
                opacity: 1;
                transition: all 0.3s ease;
            }
            
            .notification.slide-exit {
                transform: translateX(0);
                opacity: 1;
            }
            
            .notification.slide-exit-active {
                transform: translateX(-100%);
                opacity: 0;
                transition: all 0.3s ease;
            }
            
            .notification.fade-enter {
                opacity: 0;
                transform: scale(0.9);
            }
            
            .notification.fade-enter-active {
                opacity: 1;
                transform: scale(1);
                transition: all 0.3s ease;
            }
            
            .notification.fade-exit {
                opacity: 1;
                transform: scale(1);
            }
            
            .notification.fade-exit-active {
                opacity: 0;
                transform: scale(0.9);
                transition: all 0.3s ease;
            }
            
            @media (max-width: 480px) {
                .notifications-container {
                    left: 10px !important;
                    right: 10px !important;
                    max-width: none;
                }
                
                .notification {
                    margin-bottom: 8px;
                    padding: 12px 16px;
                }
            }
        `;
        
        document.head.appendChild(styles);
    },

    /**
     * عرض إشعار
     */
    show: function(options) {
        const notification = this.createNotification(options);
        this.addNotification(notification);
        return notification.id;
    },

    /**
     * عرض إشعار نجاح
     */
    success: function(message, title = 'نجح', options = {}) {
        return this.show({
            type: 'success',
            title: title,
            message: message,
            icon: 'fas fa-check-circle',
            ...options
        });
    },

    /**
     * عرض إشعار خطأ
     */
    error: function(message, title = 'خطأ', options = {}) {
        return this.show({
            type: 'error',
            title: title,
            message: message,
            icon: 'fas fa-exclamation-circle',
            duration: 8000, // مدة أطول للأخطاء
            ...options
        });
    },

    /**
     * عرض إشعار تحذير
     */
    warning: function(message, title = 'تحذير', options = {}) {
        return this.show({
            type: 'warning',
            title: title,
            message: message,
            icon: 'fas fa-exclamation-triangle',
            ...options
        });
    },

    /**
     * عرض إشعار معلومات
     */
    info: function(message, title = 'معلومات', options = {}) {
        return this.show({
            type: 'info',
            title: title,
            message: message,
            icon: 'fas fa-info-circle',
            ...options
        });
    },

    /**
     * إنشاء إشعار
     */
    createNotification: function(options) {
        const id = ++this.notificationCounter;
        const {
            type = 'info',
            title = '',
            message = '',
            icon = '',
            duration = this.config.duration,
            closable = true,
            onClick = null,
            onClose = null
        } = options;

        const notification = {
            id: id,
            type: type,
            title: title,
            message: message,
            icon: icon,
            duration: duration,
            closable: closable,
            onClick: onClick,
            onClose: onClose,
            element: null,
            timer: null
        };

        // إنشاء عنصر HTML
        notification.element = this.createNotificationElement(notification);

        return notification;
    },

    /**
     * إنشاء عنصر HTML للإشعار
     */
    createNotificationElement: function(notification) {
        const element = document.createElement('div');
        element.className = `notification ${notification.type} ${this.config.animation}-enter`;
        element.setAttribute('data-notification-id', notification.id);

        element.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    ${notification.icon ? `<i class="${notification.icon} notification-icon"></i>` : ''}
                    <span>${notification.title}</span>
                </div>
                ${notification.closable ? '<button class="notification-close" type="button">&times;</button>' : ''}
            </div>
            ${notification.message ? `<p class="notification-message">${notification.message}</p>` : ''}
            ${notification.duration > 0 ? '<div class="notification-progress"></div>' : ''}
        `;

        // إضافة أحداث
        if (notification.closable) {
            const closeBtn = element.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                this.remove(notification.id);
            });
        }

        if (notification.onClick) {
            element.addEventListener('click', notification.onClick);
            element.style.cursor = 'pointer';
        }

        return element;
    },

    /**
     * إضافة إشعار للحاوي
     */
    addNotification: function(notification) {
        const container = document.getElementById('notifications-container');
        
        // إزالة الإشعارات الزائدة
        while (this.activeNotifications.length >= this.config.maxNotifications) {
            const oldestNotification = this.activeNotifications.shift();
            this.removeElement(oldestNotification);
        }

        // إضافة الإشعار
        this.activeNotifications.push(notification);
        container.appendChild(notification.element);

        // تطبيق الحركة
        setTimeout(() => {
            notification.element.classList.remove(`${this.config.animation}-enter`);
            notification.element.classList.add(`${this.config.animation}-enter-active`);
        }, 10);

        // بدء العد التنازلي
        if (notification.duration > 0) {
            this.startTimer(notification);
        }
    },

    /**
     * بدء مؤقت الإشعار
     */
    startTimer: function(notification) {
        const progressBar = notification.element.querySelector('.notification-progress');
        
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${notification.duration}ms linear`;
            
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }

        notification.timer = setTimeout(() => {
            this.remove(notification.id);
        }, notification.duration);
    },

    /**
     * إزالة إشعار
     */
    remove: function(id) {
        const notification = this.activeNotifications.find(n => n.id === id);
        
        if (notification) {
            this.removeElement(notification);
            
            // إزالة من القائمة النشطة
            const index = this.activeNotifications.indexOf(notification);
            if (index > -1) {
                this.activeNotifications.splice(index, 1);
            }
        }
    },

    /**
     * إزالة عنصر الإشعار
     */
    removeElement: function(notification) {
        if (notification.timer) {
            clearTimeout(notification.timer);
        }

        if (notification.element && notification.element.parentNode) {
            // تطبيق حركة الخروج
            notification.element.classList.add(`${this.config.animation}-exit`);
            
            setTimeout(() => {
                notification.element.classList.add(`${this.config.animation}-exit-active`);
                
                setTimeout(() => {
                    if (notification.element.parentNode) {
                        notification.element.parentNode.removeChild(notification.element);
                    }
                    
                    // استدعاء callback الإغلاق
                    if (notification.onClose) {
                        notification.onClose();
                    }
                }, 300);
            }, 10);
        }
    },

    /**
     * إزالة جميع الإشعارات
     */
    clear: function() {
        this.activeNotifications.forEach(notification => {
            this.removeElement(notification);
        });
        
        this.activeNotifications = [];
    },

    /**
     * تحديث إعدادات الإشعارات
     */
    updateConfig: function(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // تحديث موقع الحاوي
        const container = document.getElementById('notifications-container');
        if (container) {
            container.className = `notifications-container ${this.config.position}`;
        }
    }
};

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.Notifications.init();
});

// تصدير نظام الإشعارات للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Notifications;
}

# ===================================
# Git Ignore File for Qimat Alwaed Travel System
# ===================================

# ===== Node.js =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ===== Environment Variables =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== Build Output =====
dist/
build/
*.min.js
*.min.css
bundle.*

# ===== Logs =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ===== Runtime Data =====
pids/
*.pid
*.seed
*.pid.lock

# ===== Coverage Directory =====
coverage/
*.lcov
.nyc_output

# ===== Dependency Directories =====
node_modules/
jspm_packages/

# ===== Optional npm Cache Directory =====
.npm

# ===== Optional eslint Cache =====
.eslintcache

# ===== Microbundle Cache =====
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== Optional REPL History =====
.node_repl_history

# ===== Output of 'npm pack' =====
*.tgz

# ===== Yarn Integrity File =====
.yarn-integrity

# ===== dotenv Environment Variables File =====
.env
.env.test

# ===== parcel-bundler Cache =====
.cache
.parcel-cache

# ===== Next.js Build Output =====
.next

# ===== Nuxt.js Build / Generate Output =====
.nuxt
dist

# ===== Gatsby Files =====
.cache/
public

# ===== Vuepress Build Output =====
.vuepress/dist

# ===== Serverless Directories =====
.serverless/

# ===== FuseBox Cache =====
.fusebox/

# ===== DynamoDB Local Files =====
.dynamodb/

# ===== TernJS Port File =====
.tern-port

# ===== Operating System Files =====

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== IDE and Editor Files =====

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== Backup Files =====
*.bak
*.backup
*.old
*.orig
*.tmp
*.temp

# ===== Database Files =====
*.db
*.sqlite
*.sqlite3

# ===== Application Specific =====

# Local Storage Backups
localStorage_backup_*

# User Uploads
uploads/
temp/

# Configuration Files (if containing sensitive data)
config/local.js
config/production.js

# SSL Certificates
*.pem
*.key
*.crt
*.csr

# ===== Documentation Build =====
docs/_build/
docs/build/

# ===== Test Coverage =====
coverage/
.coverage
htmlcov/

# ===== Temporary Files =====
tmp/
temp/
.tmp/

# ===== Archive Files =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== Custom Application Files =====

# System Backups
backups/
*.backup

# Export Files
exports/
*.export

# Reports
reports/generated/

# Cache
.cache/
cache/

# User Data (if sensitive)
user_data/
customer_data/

# ===== Security =====
secrets/
private/
*.secret

# ===== Performance =====
.benchmarks/

# ===== Misc =====
TODO.md
NOTES.md
.scratch/

/**
 * مكون نظام المشتريات المتقدم
 * يوفر إدارة شاملة لفواتير المشتريات، الموردين، المنتجات، عروض الأسعار، والمدفوعات
 */

const PurchasesComponent = {
    // بيانات النظام
    data: {
        currentView: 'dashboard',
        suppliers: {},
        purchaseOrders: {},
        purchaseInvoices: {},
        purchaseQuotes: {},
        purchasePayments: {},
        receivedGoods: {},
        purchaseReturns: {},
        filters: {
            dateFrom: '',
            dateTo: '',
            status: '',
            supplier: '',
            quickSearch: ''
        },
        settings: {
            currency: 'YER',
            taxRate: 0.15,
            companyInfo: {
                name: 'شركة قيمة الوعد للسفر والسياحة',
                address: 'صنعاء، اليمن',
                phone: '+967 1 234567',
                email: '<EMAIL>'
            },
            currencies: {
                'YER': { name: 'ريال يمني', symbol: 'ر.ي', rate: 1.0 },
                'SAR': { name: 'ريال سعودي', symbol: 'ر.س', rate: 0.133 },
                'USD': { name: 'دولار أمريكي', symbol: '$', rate: 0.00027 }
            }
        }
    },

    /**
     * تهيئة مكون المشتريات
     */
    init: function() {
        console.log('🛒 تهيئة نظام المشتريات...');
        this.loadPurchasesData();
        this.setupEventListeners();
        this.render();
        console.log('✅ تم تهيئة نظام المشتريات بنجاح');
    },

    /**
     * تحميل بيانات المشتريات
     */
    loadPurchasesData: function() {
        try {
            // تحميل البيانات من التخزين المحلي
            const savedData = localStorage.getItem('purchasesData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                Object.assign(this.data, parsedData);
            } else {
                // إنشاء بيانات تجريبية
                this.createSampleData();
            }
            console.log('📊 تم تحميل بيانات المشتريات');
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المشتريات:', error);
            this.createSampleData();
        }
    },

    /**
     * حفظ بيانات المشتريات
     */
    savePurchasesData: function() {
        try {
            localStorage.setItem('purchasesData', JSON.stringify(this.data));
            console.log('💾 تم حفظ بيانات المشتريات');
        } catch (error) {
            console.error('❌ خطأ في حفظ بيانات المشتريات:', error);
        }
    },

    /**
     * إنشاء بيانات تجريبية
     */
    createSampleData: function() {
        // موردين تجريبيين
        this.data.suppliers = {
            'SUP001': {
                id: 'SUP001',
                name: 'شركة الخطوط الجوية اليمنية',
                type: 'company',
                email: '<EMAIL>',
                phone: '+967 1 250000',
                address: 'صنعاء، اليمن',
                city: 'صنعاء',
                country: 'اليمن',
                taxNumber: '*********',
                paymentTerms: 30,
                currency: 'YER',
                totalPurchases: 2500000,
                status: 'active',
                createdAt: '2024-01-15T10:00:00Z'
            },
            'SUP002': {
                id: 'SUP002',
                name: 'فندق موفنبيك صنعاء',
                type: 'company',
                email: '<EMAIL>',
                phone: '+967 1 546666',
                address: 'شارع الزبيري، صنعاء',
                city: 'صنعاء',
                country: 'اليمن',
                taxNumber: '*********',
                paymentTerms: 15,
                currency: 'USD',
                totalPurchases: 15000,
                status: 'active',
                createdAt: '2024-01-20T14:30:00Z'
            },
            'SUP003': {
                id: 'SUP003',
                name: 'شركة النقل السياحي',
                type: 'company',
                email: '<EMAIL>',
                phone: '+967 1 333444',
                address: 'شارع الستين، صنعاء',
                city: 'صنعاء',
                country: 'اليمن',
                taxNumber: '*********',
                paymentTerms: 7,
                currency: 'YER',
                totalPurchases: 850000,
                status: 'active',
                createdAt: '2024-02-01T09:15:00Z'
            }
        };

        // فواتير مشتريات تجريبية
        this.data.purchaseInvoices = {
            'PI001': {
                id: 'PI001',
                number: 'PI-2024-001',
                supplierId: 'SUP001',
                date: '2024-03-01',
                dueDate: '2024-03-31',
                status: 'pending',
                currency: 'YER',
                items: [
                    {
                        description: 'تذاكر طيران - صنعاء/القاهرة',
                        quantity: 10,
                        unitPrice: 45000,
                        total: 450000
                    },
                    {
                        description: 'تذاكر طيران - صنعاء/دبي',
                        quantity: 5,
                        unitPrice: 55000,
                        total: 275000
                    }
                ],
                subtotal: 725000,
                taxAmount: 108750,
                total: 833750,
                paidAmount: 0,
                notes: 'فواتير تذاكر الطيران للعملاء',
                createdAt: '2024-03-01T10:00:00Z'
            },
            'PI002': {
                id: 'PI002',
                number: 'PI-2024-002',
                supplierId: 'SUP002',
                date: '2024-03-05',
                dueDate: '2024-03-20',
                status: 'paid',
                currency: 'USD',
                items: [
                    {
                        description: 'حجز غرف فندقية - 3 ليالي',
                        quantity: 8,
                        unitPrice: 120,
                        total: 960
                    }
                ],
                subtotal: 960,
                taxAmount: 144,
                total: 1104,
                paidAmount: 1104,
                notes: 'حجوزات فندقية للعملاء',
                createdAt: '2024-03-05T14:30:00Z'
            }
        };

        // أوامر شراء تجريبية
        this.data.purchaseOrders = {
            'PO001': {
                id: 'PO001',
                number: 'PO-2024-001',
                supplierId: 'SUP003',
                date: '2024-03-10',
                expectedDate: '2024-03-15',
                status: 'approved',
                currency: 'YER',
                items: [
                    {
                        description: 'خدمات نقل سياحي',
                        quantity: 20,
                        unitPrice: 15000,
                        total: 300000
                    }
                ],
                subtotal: 300000,
                taxAmount: 45000,
                total: 345000,
                notes: 'خدمات نقل للجولات السياحية',
                createdAt: '2024-03-10T11:00:00Z'
            }
        };

        console.log('📝 تم إنشاء بيانات المشتريات التجريبية');
    },

    /**
     * إعداد معالجات الأحداث
     */
    setupEventListeners: function() {
        // معالجات الأحداث العامة
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action]')) {
                const action = e.target.getAttribute('data-action');
                this.handleAction(action, e.target);
            }
        });
    },

    /**
     * معالج الإجراءات
     */
    handleAction: function(action, element) {
        switch (action) {
            case 'show-dashboard':
                this.showDashboard();
                break;
            case 'show-suppliers':
                this.showSuppliers();
                break;
            case 'show-purchase-invoices':
                this.showPurchaseInvoices();
                break;
            case 'show-purchase-orders':
                this.showPurchaseOrders();
                break;
            case 'show-purchase-quotes':
                this.showPurchaseQuotes();
                break;
            case 'show-purchase-payments':
                this.showPurchasePayments();
                break;
            case 'show-received-goods':
                this.showReceivedGoods();
                break;
            case 'show-purchase-returns':
                this.showPurchaseReturns();
                break;
            case 'show-purchase-reports':
                this.showPurchaseReports();
                break;
            case 'show-purchase-settings':
                this.showPurchaseSettings();
                break;
            default:
                console.warn('إجراء غير معروف:', action);
        }
    },

    /**
     * عرض النظام
     */
    render: function() {
        const container = document.getElementById('purchases-container');
        if (!container) {
            console.error('عنصر purchases-container غير موجود');
            return;
        }

        container.innerHTML = this.renderPurchasesLayout();
        this.showDashboard(); // عرض لوحة التحكم افتراضياً
    },

    /**
     * عرض تخطيط المشتريات
     */
    renderPurchasesLayout: function() {
        return `
            <div class="purchases-management">
                <!-- شريط التنقل -->
                <nav class="purchases-navbar">
                    <div class="container-fluid">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="purchases-brand">
                                <h4><i class="fas fa-shopping-cart me-2"></i>نظام المشتريات</h4>
                            </div>
                            <div class="purchases-nav-links">
                                <button class="nav-btn active" data-action="show-dashboard">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>لوحة التحكم</span>
                                </button>
                                <button class="nav-btn" data-action="show-suppliers">
                                    <i class="fas fa-truck"></i>
                                    <span>الموردين</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-invoices">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>فواتير المشتريات</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-orders">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span>أوامر الشراء</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-quotes">
                                    <i class="fas fa-file-contract"></i>
                                    <span>عروض الأسعار</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-payments">
                                    <i class="fas fa-credit-card"></i>
                                    <span>المدفوعات</span>
                                </button>
                                <button class="nav-btn" data-action="show-received-goods">
                                    <i class="fas fa-boxes"></i>
                                    <span>البضائع المستلمة</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-returns">
                                    <i class="fas fa-undo"></i>
                                    <span>مرتجعات المشتريات</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-reports">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>التقارير</span>
                                </button>
                                <button class="nav-btn" data-action="show-purchase-settings">
                                    <i class="fas fa-cog"></i>
                                    <span>الإعدادات</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- المحتوى الرئيسي -->
                <div class="purchases-content">
                    <div id="purchases-main-content">
                        <!-- سيتم تحميل المحتوى هنا -->
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض لوحة التحكم
     */
    showDashboard: function() {
        this.data.currentView = 'dashboard';
        this.updateActiveNavButton('show-dashboard');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = this.renderDashboard();

        // تحديث الإحصائيات
        this.updateDashboardStats();
    },

    /**
     * عرض لوحة التحكم
     */
    renderDashboard: function() {
        const stats = this.calculateDashboardStats();

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-tachometer-alt me-2 text-primary"></i>لوحة تحكم المشتريات</h2>
                        <p class="text-muted mb-0">نظرة شاملة على عمليات المشتريات والموردين</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.PurchasesComponent.showCreatePurchaseOrderModal()">
                            <i class="fas fa-plus me-1"></i>أمر شراء جديد
                        </button>
                        <button class="btn btn-success" onclick="window.PurchasesComponent.showCreatePurchaseInvoiceModal()">
                            <i class="fas fa-file-invoice me-1"></i>فاتورة شراء جديدة
                        </button>
                        <button class="btn btn-info" onclick="window.PurchasesComponent.showCreateSupplierModal()">
                            <i class="fas fa-truck me-1"></i>مورد جديد
                        </button>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary bg-gradient rounded-circle p-3">
                                            <i class="fas fa-file-invoice text-white fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small text-muted">إجمالي المشتريات</div>
                                        <div class="h5 mb-0" id="total-purchases">${this.formatAmount(stats.totalPurchases)}</div>
                                        <div class="small text-success">
                                            <i class="fas fa-arrow-up"></i> ${stats.purchasesGrowth}% هذا الشهر
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-gradient rounded-circle p-3">
                                            <i class="fas fa-truck text-white fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small text-muted">عدد الموردين</div>
                                        <div class="h5 mb-0" id="total-suppliers">${stats.totalSuppliers}</div>
                                        <div class="small text-info">
                                            <i class="fas fa-users"></i> ${stats.activeSuppliers} نشط
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-warning bg-gradient rounded-circle p-3">
                                            <i class="fas fa-clock text-white fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small text-muted">فواتير معلقة</div>
                                        <div class="h5 mb-0" id="pending-invoices">${stats.pendingInvoices}</div>
                                        <div class="small text-warning">
                                            <i class="fas fa-exclamation-triangle"></i> تحتاج متابعة
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-danger bg-gradient rounded-circle p-3">
                                            <i class="fas fa-exclamation-circle text-white fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small text-muted">فواتير متأخرة</div>
                                        <div class="h5 mb-0" id="overdue-invoices">${stats.overdueInvoices}</div>
                                        <div class="small text-danger">
                                            <i class="fas fa-calendar-times"></i> تجاوزت الموعد
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- محول العملات والإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-gradient-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>محول العملات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">المبلغ</label>
                                        <input type="number" class="form-control" id="currency-amount" placeholder="0.00" step="0.01">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">من</label>
                                        <select class="form-select" id="currency-from">
                                            <option value="YER">ريال يمني</option>
                                            <option value="SAR">ريال سعودي</option>
                                            <option value="USD">دولار أمريكي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">إلى</label>
                                        <select class="form-select" id="currency-to">
                                            <option value="SAR">ريال سعودي</option>
                                            <option value="YER">ريال يمني</option>
                                            <option value="USD">دولار أمريكي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary" onclick="window.PurchasesComponent.convertCurrency()">
                                        <i class="fas fa-calculator me-1"></i>تحويل
                                    </button>
                                    <div class="mt-2">
                                        <div class="alert alert-info mb-0" id="conversion-result" style="display: none;">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <span id="conversion-text"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-gradient-success text-white">
                                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.showCreatePurchaseOrderModal()">
                                        <i class="fas fa-clipboard-list me-2"></i>إنشاء أمر شراء جديد
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.PurchasesComponent.showCreatePurchaseInvoiceModal()">
                                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة شراء جديدة
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.PurchasesComponent.showCreateSupplierModal()">
                                        <i class="fas fa-truck me-2"></i>إضافة مورد جديد
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="window.PurchasesComponent.showPurchaseReports()">
                                        <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * حساب إحصائيات لوحة التحكم
     */
    calculateDashboardStats: function() {
        const invoices = Object.values(this.data.purchaseInvoices);
        const suppliers = Object.values(this.data.suppliers);

        const totalPurchases = invoices.reduce((sum, invoice) => {
            return sum + this.convertToBaseCurrency(invoice.total, invoice.currency);
        }, 0);

        const pendingInvoices = invoices.filter(invoice => invoice.status === 'pending').length;
        const overdueInvoices = invoices.filter(invoice => {
            return invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();
        }).length;

        const activeSuppliers = suppliers.filter(supplier => supplier.status === 'active').length;

        return {
            totalPurchases,
            totalSuppliers: suppliers.length,
            activeSuppliers,
            pendingInvoices,
            overdueInvoices,
            purchasesGrowth: 12.5 // نسبة نمو تجريبية
        };
    },

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats: function() {
        // تحديث الإحصائيات في الوقت الفعلي
        setTimeout(() => {
            const stats = this.calculateDashboardStats();

            const totalPurchasesEl = document.getElementById('total-purchases');
            const totalSuppliersEl = document.getElementById('total-suppliers');
            const pendingInvoicesEl = document.getElementById('pending-invoices');
            const overdueInvoicesEl = document.getElementById('overdue-invoices');

            if (totalPurchasesEl) totalPurchasesEl.textContent = this.formatAmount(stats.totalPurchases);
            if (totalSuppliersEl) totalSuppliersEl.textContent = stats.totalSuppliers;
            if (pendingInvoicesEl) pendingInvoicesEl.textContent = stats.pendingInvoices;
            if (overdueInvoicesEl) overdueInvoicesEl.textContent = stats.overdueInvoices;
        }, 100);
    },

    /**
     * عرض آخر الفواتير
     */
    renderRecentInvoices: function() {
        const invoices = Object.values(this.data.purchaseInvoices)
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);

        if (invoices.length === 0) {
            return '<tr><td colspan="5" class="text-center text-muted">لا توجد فواتير</td></tr>';
        }

        return invoices.map(invoice => {
            const supplier = this.data.suppliers[invoice.supplierId];
            const statusClass = this.getStatusClass(invoice.status);
            const statusText = this.getStatusText(invoice.status);

            return `
                <tr>
                    <td><strong>${invoice.number}</strong></td>
                    <td>${supplier ? supplier.name : 'غير محدد'}</td>
                    <td>${this.formatDate(invoice.date)}</td>
                    <td>${this.formatAmount(invoice.total, invoice.currency)}</td>
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                </tr>
            `;
        }).join('');
    },

    /**
     * عرض أهم الموردين
     */
    renderTopSuppliers: function() {
        const suppliers = Object.values(this.data.suppliers)
            .sort((a, b) => (b.totalPurchases || 0) - (a.totalPurchases || 0))
            .slice(0, 5);

        if (suppliers.length === 0) {
            return '<div class="text-center text-muted">لا يوجد موردين</div>';
        }

        return suppliers.map(supplier => `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <div class="fw-bold">${supplier.name}</div>
                    <small class="text-muted">${supplier.city || 'غير محدد'}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold text-success">${this.formatAmount(supplier.totalPurchases || 0, supplier.currency)}</div>
                    <small class="text-muted">إجمالي المشتريات</small>
                </div>
            </div>
        `).join('');
    },

    /**
     * تحديث الزر النشط في التنقل
     */
    updateActiveNavButton: function(action) {
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.querySelector(`[data-action="${action}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    },

    /**
     * تحويل العملة
     */
    convertCurrency: function() {
        const amount = parseFloat(document.getElementById('currency-amount').value) || 0;
        const fromCurrency = document.getElementById('currency-from').value;
        const toCurrency = document.getElementById('currency-to').value;

        if (amount <= 0) {
            this.showNotification('يرجى إدخال مبلغ صحيح', 'warning');
            return;
        }

        const convertedAmount = this.convertBetweenCurrencies(amount, fromCurrency, toCurrency);
        const fromSymbol = this.data.settings.currencies[fromCurrency].symbol;
        const toSymbol = this.data.settings.currencies[toCurrency].symbol;

        const resultText = `${amount.toFixed(2)} ${fromSymbol} = ${convertedAmount.toFixed(2)} ${toSymbol}`;

        document.getElementById('conversion-text').textContent = resultText;
        document.getElementById('conversion-result').style.display = 'block';
    },

    /**
     * تحويل بين العملات
     */
    convertBetweenCurrencies: function(amount, fromCurrency, toCurrency) {
        if (fromCurrency === toCurrency) return amount;

        const fromRate = this.data.settings.currencies[fromCurrency].rate;
        const toRate = this.data.settings.currencies[toCurrency].rate;

        // تحويل إلى العملة الأساسية أولاً ثم إلى العملة المطلوبة
        const baseAmount = amount / fromRate;
        return baseAmount * toRate;
    },

    /**
     * تحويل إلى العملة الأساسية
     */
    convertToBaseCurrency: function(amount, currency) {
        if (currency === 'YER') return amount;
        const rate = this.data.settings.currencies[currency]?.rate || 1;
        return amount / rate;
    },

    /**
     * تنسيق المبلغ
     */
    formatAmount: function(amount, currency = 'YER') {
        const symbol = this.data.settings.currencies[currency]?.symbol || 'ر.ي';
        return `${amount.toLocaleString('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${symbol}`;
    },

    /**
     * تنسيق التاريخ
     */
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    },

    /**
     * الحصول على فئة الحالة
     */
    getStatusClass: function(status) {
        const statusClasses = {
            'pending': 'bg-warning',
            'approved': 'bg-info',
            'paid': 'bg-success',
            'overdue': 'bg-danger',
            'cancelled': 'bg-secondary',
            'draft': 'bg-light text-dark'
        };
        return statusClasses[status] || 'bg-secondary';
    },

    /**
     * الحصول على نص الحالة
     */
    getStatusText: function(status) {
        const statusTexts = {
            'pending': 'معلقة',
            'approved': 'معتمدة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغاة',
            'draft': 'مسودة'
        };
        return statusTexts[status] || 'غير محدد';
    },

    /**
     * عرض إشعار
     */
    showNotification: function(message, type = 'info') {
        // إنشاء إشعار بسيط
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    },

    /**
     * عرض صفحة الموردين
     */
    showSuppliers: function() {
        this.data.currentView = 'suppliers';
        this.updateActiveNavButton('show-suppliers');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = this.renderSuppliers();
    },

    /**
     * عرض صفحة الموردين
     */
    renderSuppliers: function() {
        const suppliers = Object.values(this.data.suppliers);

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-truck me-2 text-info"></i>إدارة الموردين</h2>
                        <p class="text-muted mb-0">إدارة شاملة لقاعدة بيانات الموردين</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-info" onclick="window.PurchasesComponent.showCreateSupplierModal()">
                            <i class="fas fa-plus me-1"></i>مورد جديد
                        </button>
                        <button class="btn btn-outline-info" onclick="window.PurchasesComponent.refreshSuppliers()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.exportSuppliers()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">البحث السريع</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="supplier-search" placeholder="ابحث عن مورد...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المدينة</label>
                                <select class="form-select" id="supplier-city-filter">
                                    <option value="">جميع المدن</option>
                                    <option value="صنعاء">صنعاء</option>
                                    <option value="عدن">عدن</option>
                                    <option value="تعز">تعز</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="supplier-status-filter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block w-100" onclick="window.PurchasesComponent.filterSuppliers()">
                                    <i class="fas fa-filter me-1"></i>فلترة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الموردين -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>النوع</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>المدينة</th>
                                        <th>إجمالي المشتريات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${suppliers.map(supplier => `
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-info bg-gradient rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-truck text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong>${supplier.name}</strong>
                                                        <br><small class="text-muted">${supplier.id}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge ${supplier.type === 'company' ? 'bg-primary' : 'bg-secondary'}">
                                                    ${supplier.type === 'company' ? 'شركة' : 'فرد'}
                                                </span>
                                            </td>
                                            <td>${supplier.email || 'غير محدد'}</td>
                                            <td>${supplier.phone || 'غير محدد'}</td>
                                            <td>${supplier.city || 'غير محدد'}</td>
                                            <td>${this.formatAmount(supplier.totalPurchases || 0, supplier.currency)}</td>
                                            <td>
                                                <span class="badge ${supplier.status === 'active' ? 'bg-success' : 'bg-secondary'}">
                                                    ${supplier.status === 'active' ? 'نشط' : 'غير نشط'}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.viewSupplier('${supplier.id}')" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="window.PurchasesComponent.editSupplier('${supplier.id}')" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="window.PurchasesComponent.createPurchaseOrderForSupplier('${supplier.id}')" title="أمر شراء">
                                                        <i class="fas fa-clipboard-list"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="window.PurchasesComponent.deleteSupplier('${supplier.id}')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${suppliers.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد موردين</h5>
                                <p class="text-muted">ابدأ بإضافة مورد جديد</p>
                                <button class="btn btn-info" onclick="window.PurchasesComponent.showCreateSupplierModal()">
                                    <i class="fas fa-plus me-1"></i>إضافة مورد جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة فواتير المشتريات
     */
    showPurchaseInvoices: function() {
        this.data.currentView = 'purchase-invoices';
        this.updateActiveNavButton('show-purchase-invoices');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = this.renderPurchaseInvoices();
    },

    /**
     * عرض صفحة فواتير المشتريات
     */
    renderPurchaseInvoices: function() {
        const invoices = Object.values(this.data.purchaseInvoices);

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-file-invoice me-2 text-success"></i>فواتير المشتريات</h2>
                        <p class="text-muted mb-0">إدارة شاملة لفواتير المشتريات والمدفوعات</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-success" onclick="window.PurchasesComponent.showCreatePurchaseInvoiceModal()">
                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                        </button>
                        <button class="btn btn-outline-info" onclick="window.PurchasesComponent.refreshPurchaseInvoices()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.exportPurchaseInvoices()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث السريع</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="invoice-search" placeholder="رقم الفاتورة أو المورد...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="invoice-date-from">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="invoice-date-to">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="invoice-status-filter">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">معلقة</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="overdue">متأخرة</option>
                                    <option value="cancelled">ملغاة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">المورد</label>
                                <select class="form-select" id="invoice-supplier-filter">
                                    <option value="">جميع الموردين</option>
                                    ${Object.values(this.data.suppliers).map(supplier =>
                                        `<option value="${supplier.id}">${supplier.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block w-100" onclick="window.PurchasesComponent.filterPurchaseInvoices()">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الفواتير -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>المورد</th>
                                        <th>التاريخ</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>المبلغ</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${invoices.map(invoice => {
                                        const supplier = this.data.suppliers[invoice.supplierId];
                                        const remaining = invoice.total - (invoice.paidAmount || 0);
                                        const isOverdue = invoice.status === 'pending' && new Date(invoice.dueDate) < new Date();

                                        return `
                                            <tr class="${isOverdue ? 'table-warning' : ''}">
                                                <td>
                                                    <strong>${invoice.number}</strong>
                                                    <br><small class="text-muted">${invoice.id}</small>
                                                </td>
                                                <td>${supplier ? supplier.name : 'غير محدد'}</td>
                                                <td>${this.formatDate(invoice.date)}</td>
                                                <td>${this.formatDate(invoice.dueDate)}</td>
                                                <td>${this.formatAmount(invoice.total, invoice.currency)}</td>
                                                <td>${this.formatAmount(invoice.paidAmount || 0, invoice.currency)}</td>
                                                <td>
                                                    <span class="${remaining > 0 ? 'text-danger' : 'text-success'}">
                                                        ${this.formatAmount(remaining, invoice.currency)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge ${this.getStatusClass(isOverdue ? 'overdue' : invoice.status)}">
                                                        ${this.getStatusText(isOverdue ? 'overdue' : invoice.status)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.viewPurchaseInvoice('${invoice.id}')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="window.PurchasesComponent.editPurchaseInvoice('${invoice.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" onclick="window.PurchasesComponent.printPurchaseInvoice('${invoice.id}')" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        ${remaining > 0 ? `
                                                            <button class="btn btn-outline-warning" onclick="window.PurchasesComponent.recordPayment('${invoice.id}')" title="تسجيل دفعة">
                                                                <i class="fas fa-credit-card"></i>
                                                            </button>
                                                        ` : ''}
                                                        <button class="btn btn-outline-danger" onclick="window.PurchasesComponent.deletePurchaseInvoice('${invoice.id}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${invoices.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد فواتير مشتريات</h5>
                                <p class="text-muted">ابدأ بإنشاء فاتورة شراء جديدة</p>
                                <button class="btn btn-success" onclick="window.PurchasesComponent.showCreatePurchaseInvoiceModal()">
                                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * عرض صفحة أوامر الشراء
     */
    showPurchaseOrders: function() {
        this.data.currentView = 'purchase-orders';
        this.updateActiveNavButton('show-purchase-orders');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = this.renderPurchaseOrders();
    },

    /**
     * عرض صفحة أوامر الشراء
     */
    renderPurchaseOrders: function() {
        const orders = Object.values(this.data.purchaseOrders);

        return `
            <div class="container-fluid">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-clipboard-list me-2 text-primary"></i>أوامر الشراء</h2>
                        <p class="text-muted mb-0">إدارة أوامر الشراء والموافقات</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="window.PurchasesComponent.showCreatePurchaseOrderModal()">
                            <i class="fas fa-plus me-1"></i>أمر شراء جديد
                        </button>
                        <button class="btn btn-outline-info" onclick="window.PurchasesComponent.refreshPurchaseOrders()">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.exportPurchaseOrders()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- قائمة أوامر الشراء -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الأمر</th>
                                        <th>المورد</th>
                                        <th>التاريخ</th>
                                        <th>التاريخ المتوقع</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${orders.map(order => {
                                        const supplier = this.data.suppliers[order.supplierId];

                                        return `
                                            <tr>
                                                <td>
                                                    <strong>${order.number}</strong>
                                                    <br><small class="text-muted">${order.id}</small>
                                                </td>
                                                <td>${supplier ? supplier.name : 'غير محدد'}</td>
                                                <td>${this.formatDate(order.date)}</td>
                                                <td>${this.formatDate(order.expectedDate)}</td>
                                                <td>${this.formatAmount(order.total, order.currency)}</td>
                                                <td>
                                                    <span class="badge ${this.getStatusClass(order.status)}">
                                                        ${this.getStatusText(order.status)}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="window.PurchasesComponent.viewPurchaseOrder('${order.id}')" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="window.PurchasesComponent.editPurchaseOrder('${order.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" onclick="window.PurchasesComponent.printPurchaseOrder('${order.id}')" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        ${order.status === 'approved' ? `
                                                            <button class="btn btn-outline-warning" onclick="window.PurchasesComponent.convertToInvoice('${order.id}')" title="تحويل لفاتورة">
                                                                <i class="fas fa-file-invoice"></i>
                                                            </button>
                                                        ` : ''}
                                                        <button class="btn btn-outline-danger" onclick="window.PurchasesComponent.deletePurchaseOrder('${order.id}')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${orders.length === 0 ? `
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد أوامر شراء</h5>
                                <p class="text-muted">ابدأ بإنشاء أمر شراء جديد</p>
                                <button class="btn btn-primary" onclick="window.PurchasesComponent.showCreatePurchaseOrderModal()">
                                    <i class="fas fa-plus me-1"></i>إنشاء أمر شراء جديد
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    // وظائف مؤقتة للصفحات الأخرى
    showPurchaseQuotes: function() {
        this.data.currentView = 'purchase-quotes';
        this.updateActiveNavButton('show-purchase-quotes');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-file-contract fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">عروض أسعار المشتريات</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    showPurchasePayments: function() {
        this.data.currentView = 'purchase-payments';
        this.updateActiveNavButton('show-purchase-payments');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">مدفوعات المشتريات</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    showReceivedGoods: function() {
        this.data.currentView = 'received-goods';
        this.updateActiveNavButton('show-received-goods');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">البضائع المستلمة</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    showPurchaseReturns: function() {
        this.data.currentView = 'purchase-returns';
        this.updateActiveNavButton('show-purchase-returns');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-undo fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">مرتجعات المشتريات</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    showPurchaseReports: function() {
        this.data.currentView = 'purchase-reports';
        this.updateActiveNavButton('show-purchase-reports');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-chart-bar fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">تقارير المشتريات</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    showPurchaseSettings: function() {
        this.data.currentView = 'purchase-settings';
        this.updateActiveNavButton('show-purchase-settings');

        const content = document.getElementById('purchases-main-content');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="text-center py-5">
                    <i class="fas fa-cog fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">إعدادات المشتريات</h3>
                    <p class="text-muted fs-5">قريباً...</p>
                    <button class="btn btn-primary" onclick="window.PurchasesComponent.showDashboard()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للوحة التحكم
                    </button>
                </div>
            </div>
        `;
    },

    // وظائف النوافذ المنبثقة (مؤقتة)
    showCreateSupplierModal: function() {
        this.showNotification('نافذة إضافة مورد جديد قريباً...', 'info');
    },

    showCreatePurchaseOrderModal: function() {
        this.showNotification('نافذة إنشاء أمر شراء جديد قريباً...', 'info');
    },

    showCreatePurchaseInvoiceModal: function() {
        this.showNotification('نافذة إنشاء فاتورة شراء جديدة قريباً...', 'info');
    },

    // وظائف الإجراءات (مؤقتة)
    viewSupplier: function(supplierId) {
        this.showNotification(`عرض تفاصيل المورد ${supplierId}`, 'info');
    },

    editSupplier: function(supplierId) {
        this.showNotification(`تعديل المورد ${supplierId}`, 'info');
    },

    deleteSupplier: function(supplierId) {
        if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
            delete this.data.suppliers[supplierId];
            this.savePurchasesData();
            this.showSuppliers();
            this.showNotification('تم حذف المورد بنجاح', 'success');
        }
    },

    viewPurchaseInvoice: function(invoiceId) {
        this.showNotification(`عرض فاتورة الشراء ${invoiceId}`, 'info');
    },

    editPurchaseInvoice: function(invoiceId) {
        this.showNotification(`تعديل فاتورة الشراء ${invoiceId}`, 'info');
    },

    deletePurchaseInvoice: function(invoiceId) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            delete this.data.purchaseInvoices[invoiceId];
            this.savePurchasesData();
            this.showPurchaseInvoices();
            this.showNotification('تم حذف الفاتورة بنجاح', 'success');
        }
    },

    // وظائف التحديث
    refreshSuppliers: function() {
        this.showSuppliers();
        this.showNotification('تم تحديث قائمة الموردين', 'success');
    },

    refreshPurchaseInvoices: function() {
        this.showPurchaseInvoices();
        this.showNotification('تم تحديث قائمة الفواتير', 'success');
    },

    refreshPurchaseOrders: function() {
        this.showPurchaseOrders();
        this.showNotification('تم تحديث قائمة أوامر الشراء', 'success');
    }
};

// تصدير المكون للاستخدام العام
window.PurchasesComponent = PurchasesComponent;

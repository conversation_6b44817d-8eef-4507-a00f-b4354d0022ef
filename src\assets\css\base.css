/* ===================================
   Base Styles - الأنماط الأساسية
   =================================== */

/* ===== إعادة تعيين الأنماط ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ===== HTML & Body ===== */
html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--system-bg);
  direction: rtl;
  text-align: right;
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== العناوين ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* ===== الفقرات والنصوص ===== */
p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

.lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-light {
  color: var(--text-light) !important;
}

.text-white {
  color: var(--text-white) !important;
}

/* ===== الروابط ===== */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-all);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== القوائم ===== */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-right: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
}

/* ===== الصور ===== */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* ===== الجداول ===== */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-lg);
}

th, td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: right;
  border-bottom: var(--border-width) solid var(--border-color);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* ===== النماذج ===== */
input,
textarea,
select,
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* ===== الأزرار الأساسية ===== */
button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font: inherit;
  color: inherit;
  text-decoration: none;
  outline: none;
}

button:disabled {
  cursor: not-allowed;
  opacity: var(--opacity-50);
}

/* ===== الفواصل ===== */
hr {
  border: none;
  height: var(--border-width);
  background-color: var(--border-color);
  margin: var(--spacing-lg) 0;
}

/* ===== الكود ===== */
code,
pre {
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-sm);
}

code {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  color: var(--danger-color);
}

pre {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin-bottom: var(--spacing-md);
}

/* ===== الاقتباسات ===== */
blockquote {
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border-right: 4px solid var(--primary-color);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
}

blockquote p:last-child {
  margin-bottom: 0;
}

/* ===== الأدوات المساعدة ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* ===== التمرير المخصص ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--border-radius);
  transition: var(--transition-all);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* ===== التحديد ===== */
::selection {
  background-color: rgba(var(--primary-rgb), 0.2);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: rgba(var(--primary-rgb), 0.2);
  color: var(--text-primary);
}

/* ===== التركيز ===== */
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== الطباعة ===== */
@media print {
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: black !important;
    page-break-after: avoid;
  }
  
  p, li {
    orphans: 3;
    widows: 3;
  }
  
  a {
    color: black !important;
    text-decoration: underline;
  }
  
  img {
    max-width: 100% !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* ===== الحركة المخفضة ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===================================
   CSS Variables - نظام المتغيرات
   =================================== */

:root {
  /* ===== الألوان الأساسية ===== */
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --primary-light: #7c8ef0;
  --primary-rgb: 102, 126, 234;
  
  --secondary-color: #764ba2;
  --secondary-dark: #6a4190;
  --secondary-light: #8355b4;
  --secondary-rgb: 118, 75, 162;
  
  --accent-color: #f093fb;
  --accent-dark: #e67fe8;
  --accent-light: #f5a7fc;
  
  /* ===== ألوان الحالة ===== */
  --success-color: #28a745;
  --success-dark: #1e7e34;
  --success-light: #34ce57;
  --success-rgb: 40, 167, 69;
  
  --danger-color: #dc3545;
  --danger-dark: #c82333;
  --danger-light: #e4606d;
  --danger-rgb: 220, 53, 69;
  
  --warning-color: #ffc107;
  --warning-dark: #e0a800;
  --warning-light: #ffcd39;
  --warning-rgb: 255, 193, 7;
  
  --info-color: #17a2b8;
  --info-dark: #138496;
  --info-light: #3fc1d8;
  --info-rgb: 23, 162, 184;
  
  /* ===== ألوان إضافية ===== */
  --purple-color: #6f42c1;
  --purple-dark: #5a32a3;
  --purple-light: #8454d1;
  
  --indigo-color: #6610f2;
  --indigo-dark: #520dc2;
  --indigo-light: #7c3aed;
  
  --pink-color: #e83e8c;
  --pink-dark: #d91a72;
  --pink-light: #ec5aa0;
  
  --orange-color: #fd7e14;
  --orange-dark: #e8650e;
  --orange-light: #fd9843;
  
  --teal-color: #20c997;
  --teal-dark: #1ba085;
  --teal-light: #46d5a7;
  
  --cyan-color: #0dcaf0;
  --cyan-dark: #0bb5d3;
  --cyan-light: #3dd5f3;
  
  /* ===== ألوان رمادية ===== */
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  
  --white: #ffffff;
  --black: #000000;
  --light: var(--gray-100);
  --dark: var(--gray-800);
  
  /* ===== ألوان النص ===== */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --text-light: var(--gray-400);
  --text-white: var(--white);
  
  /* ===== ألوان الخلفية ===== */
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-100);
  --bg-tertiary: var(--gray-200);
  --bg-dark: var(--gray-800);
  --bg-light: var(--gray-100);
  
  /* ===== التدرجات ===== */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--teal-color) 100%);
  --gradient-danger: linear-gradient(135deg, var(--danger-color) 0%, var(--pink-color) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-color) 0%, var(--orange-color) 100%);
  --gradient-info: linear-gradient(135deg, var(--info-color) 0%, var(--cyan-color) 100%);
  --gradient-purple: linear-gradient(135deg, var(--purple-color) 0%, var(--indigo-color) 100%);
  
  --gradient-light: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --gradient-dark: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
  
  /* ===== الخطوط ===== */
  --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-secondary: 'Cairo', Arial, sans-serif;
  --font-family-monospace: 'Courier New', Courier, monospace;
  
  /* ===== أحجام الخطوط ===== */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  /* ===== أوزان الخطوط ===== */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* ===== المسافات ===== */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* ===== الحدود ===== */
  --border-width: 1px;
  --border-width-thick: 2px;
  --border-color: var(--gray-300);
  --border-color-light: var(--gray-200);
  --border-color-dark: var(--gray-400);
  
  /* ===== الزوايا المدورة ===== */
  --border-radius-sm: 0.375rem;   /* 6px */
  --border-radius: 0.5rem;        /* 8px */
  --border-radius-md: 0.75rem;    /* 12px */
  --border-radius-lg: 1rem;       /* 16px */
  --border-radius-xl: 1.5rem;     /* 24px */
  --border-radius-2xl: 2rem;      /* 32px */
  --border-radius-full: 9999px;
  
  /* ===== الظلال ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* ===== الانتقالات ===== */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  --transition-all: all var(--transition-normal);
  
  /* ===== الشفافية ===== */
  --opacity-0: 0;
  --opacity-25: 0.25;
  --opacity-50: 0.5;
  --opacity-75: 0.75;
  --opacity-100: 1;
  
  /* ===== Z-Index ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* ===== أبعاد الشاشة ===== */
  --screen-sm: 576px;
  --screen-md: 768px;
  --screen-lg: 992px;
  --screen-xl: 1200px;
  --screen-2xl: 1400px;
  
  /* ===== متغيرات خاصة بالنظام ===== */
  --navbar-height: 70px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --footer-height: 60px;
  
  /* ===== ألوان خاصة بالنظام ===== */
  --system-bg: var(--gradient-light);
  --card-bg: rgba(255, 255, 255, 0.95);
  --navbar-bg: var(--gradient-primary);
  --sidebar-bg: var(--white);
  --footer-bg: var(--gray-800);
  
  /* ===== تأثيرات بصرية ===== */
  --blur-sm: blur(4px);
  --blur: blur(8px);
  --blur-md: blur(12px);
  --blur-lg: blur(16px);
  --blur-xl: blur(24px);
  
  --backdrop-blur: blur(10px);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

/* ===== متغيرات الوضع المظلم ===== */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
  --border-color: var(--gray-600);
  --card-bg: rgba(0, 0, 0, 0.3);
  --system-bg: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* ===== متغيرات الطباعة ===== */
@media print {
  :root {
    --shadow-sm: none;
    --shadow: none;
    --shadow-md: none;
    --shadow-lg: none;
    --shadow-xl: none;
    --shadow-2xl: none;
    --transition-fast: none;
    --transition-normal: none;
    --transition-slow: none;
    --transition-all: none;
  }
}

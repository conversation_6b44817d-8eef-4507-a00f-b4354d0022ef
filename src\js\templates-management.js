/**
 * ===================================
 * JavaScript لنظام إدارة القوالب المحاسبية
 * Templates Management System JavaScript
 * ===================================
 */

// بيانات القوالب
const templatesData = {
    vouchers: [
        {
            id: 'receipt_voucher_1',
            name: 'سند قبض - النموذج الأساسي',
            description: 'قالب سند قبض احترافي للمبالغ المستلمة من العملاء',
            category: 'vouchers',
            type: 'receipt',
            status: 'active',
            lastModified: '2024-01-15',
            usageCount: 45,
            preview: 'fas fa-file-invoice-dollar'
        },
        {
            id: 'payment_voucher_1',
            name: 'سند صرف - النموذج الأساسي',
            description: 'قالب سند صرف للمدفوعات والمصروفات',
            category: 'vouchers',
            type: 'payment',
            status: 'active',
            lastModified: '2024-01-14',
            usageCount: 38,
            preview: 'fas fa-file-invoice'
        },
        {
            id: 'journal_entry_1',
            name: 'قيد يومية - النموذج المتقدم',
            description: 'قالب قيد يومية شامل للعمليات المحاسبية',
            category: 'vouchers',
            type: 'journal',
            status: 'active',
            lastModified: '2024-01-13',
            usageCount: 52,
            preview: 'fas fa-book'
        },
        {
            id: 'transfer_voucher_1',
            name: 'سند تحويل - بين الحسابات',
            description: 'قالب سند تحويل للتحويلات بين الحسابات المختلفة',
            category: 'vouchers',
            type: 'transfer',
            status: 'draft',
            lastModified: '2024-01-12',
            usageCount: 12,
            preview: 'fas fa-exchange-alt'
        }
    ],
    reports: [
        {
            id: 'balance_sheet_1',
            name: 'الميزانية العمومية - التقرير الشامل',
            description: 'قالب الميزانية العمومية مع جميع التفاصيل المالية',
            category: 'reports',
            type: 'balance_sheet',
            status: 'active',
            lastModified: '2024-01-16',
            usageCount: 28,
            preview: 'fas fa-balance-scale'
        },
        {
            id: 'income_statement_1',
            name: 'قائمة الدخل - التقرير المفصل',
            description: 'قالب قائمة الدخل والأرباح والخسائر',
            category: 'reports',
            type: 'income_statement',
            status: 'active',
            lastModified: '2024-01-15',
            usageCount: 35,
            preview: 'fas fa-chart-line'
        },
        {
            id: 'cash_flow_1',
            name: 'تقرير التدفق النقدي',
            description: 'قالب تقرير التدفق النقدي الشامل',
            category: 'reports',
            type: 'cash_flow',
            status: 'active',
            lastModified: '2024-01-14',
            usageCount: 22,
            preview: 'fas fa-coins'
        }
    ],
    invoices: [
        {
            id: 'travel_invoice_1',
            name: 'فاتورة السفر - النموذج الاحترافي',
            description: 'قالب فاتورة مخصص لخدمات السفر والسياحة',
            category: 'invoices',
            type: 'travel_invoice',
            status: 'active',
            lastModified: '2024-01-17',
            usageCount: 67,
            preview: 'fas fa-plane'
        },
        {
            id: 'service_invoice_1',
            name: 'فاتورة الخدمات - متعددة الخدمات',
            description: 'قالب فاتورة للخدمات المتنوعة (فيزا، فنادق، نقل)',
            category: 'invoices',
            type: 'service_invoice',
            status: 'active',
            lastModified: '2024-01-16',
            usageCount: 43,
            preview: 'fas fa-concierge-bell'
        },
        {
            id: 'receipt_1',
            name: 'إيصال الدفع - النموذج البسيط',
            description: 'قالب إيصال دفع بسيط وواضح',
            category: 'invoices',
            type: 'receipt',
            status: 'active',
            lastModified: '2024-01-15',
            usageCount: 89,
            preview: 'fas fa-receipt'
        }
    ],
    accounts: [
        {
            id: 'customer_statement_1',
            name: 'كشف حساب العميل - التقرير الشامل',
            description: 'قالب كشف حساب مفصل للعملاء',
            category: 'accounts',
            type: 'customer_statement',
            status: 'active',
            lastModified: '2024-01-18',
            usageCount: 56,
            preview: 'fas fa-user-tie'
        },
        {
            id: 'supplier_statement_1',
            name: 'كشف حساب المورد',
            description: 'قالب كشف حساب للموردين والشركاء',
            category: 'accounts',
            type: 'supplier_statement',
            status: 'active',
            lastModified: '2024-01-17',
            usageCount: 34,
            preview: 'fas fa-truck'
        },
        {
            id: 'agent_statement_1',
            name: 'كشف حساب الوكيل',
            description: 'قالب كشف حساب خاص بالوكلاء والعمولات',
            category: 'accounts',
            type: 'agent_statement',
            status: 'draft',
            lastModified: '2024-01-16',
            usageCount: 18,
            preview: 'fas fa-handshake'
        }
    ]
};

// المتغيرات العامة
let currentCategory = 'all';
let filteredTemplates = [];

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeTemplatesSystem();
    loadTemplates();
    setupEventListeners();
});

/**
 * تهيئة نظام القوالب
 */
function initializeTemplatesSystem() {
    console.log('تم تحميل نظام إدارة القوالب بنجاح');
    
    // تحديث الإحصائيات
    updateTemplatesStats();
    
    // تحميل القوالب الافتراضية
    loadDefaultTemplates();
}

/**
 * تحديث إحصائيات القوالب
 */
function updateTemplatesStats() {
    const vouchersCount = templatesData.vouchers.length;
    const reportsCount = templatesData.reports.length;
    const customCount = templatesData.invoices.length + templatesData.accounts.length;
    const totalUsage = getAllTemplates().reduce((sum, template) => sum + template.usageCount, 0);
    
    // تحديث الأرقام مع تأثير العد
    animateNumber(document.querySelector('.stat-card.stat-primary .stat-number'), vouchersCount);
    animateNumber(document.querySelector('.stat-card.stat-success .stat-number'), reportsCount);
    animateNumber(document.querySelector('.stat-card.stat-info .stat-number'), customCount);
    animateNumber(document.querySelector('.stat-card.stat-warning .stat-number'), totalUsage);
}

/**
 * تحميل القوالب الافتراضية
 */
function loadDefaultTemplates() {
    // حفظ القوالب في التخزين المحلي إذا لم تكن موجودة
    if (!localStorage.getItem('templates_data')) {
        localStorage.setItem('templates_data', JSON.stringify(templatesData));
    }
}

/**
 * تحميل وعرض القوالب
 */
function loadTemplates() {
    const templatesGrid = document.getElementById('templatesGrid');
    const templates = getFilteredTemplates();
    
    if (templates.length === 0) {
        templatesGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد قوالب تطابق البحث</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
            </div>
        `;
        return;
    }
    
    templatesGrid.innerHTML = templates.map(template => createTemplateCard(template)).join('');
    
    // إضافة تأثيرات الظهور
    const cards = templatesGrid.querySelectorAll('.template-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * إنشاء بطاقة قالب
 */
function createTemplateCard(template) {
    const statusClass = template.status === 'active' ? 'active' : 'draft';
    const statusText = template.status === 'active' ? 'نشط' : 'مسودة';
    
    return `
        <div class="template-card" data-template-id="${template.id}" onclick="openTemplate('${template.id}')">
            <div class="template-preview">
                <i class="${template.preview}"></i>
            </div>
            <div class="template-info">
                <h3 class="template-title">${template.name}</h3>
                <p class="template-description">${template.description}</p>
                <div class="template-meta">
                    <span class="template-category">${getCategoryName(template.category)}</span>
                    <span class="template-status ${statusClass}">${statusText}</span>
                </div>
                <div class="template-actions">
                    <button class="btn btn-primary btn-sm" onclick="event.stopPropagation(); editTemplate('${template.id}')">
                        <i class="fas fa-edit"></i> تحرير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="event.stopPropagation(); previewTemplate('${template.id}')">
                        <i class="fas fa-eye"></i> معاينة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="event.stopPropagation(); duplicateTemplate('${template.id}')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * الحصول على اسم الفئة
 */
function getCategoryName(category) {
    const names = {
        vouchers: 'السندات',
        reports: 'التقارير',
        invoices: 'الفواتير',
        accounts: 'الحسابات'
    };
    return names[category] || category;
}

/**
 * الحصول على جميع القوالب
 */
function getAllTemplates() {
    return [
        ...templatesData.vouchers,
        ...templatesData.reports,
        ...templatesData.invoices,
        ...templatesData.accounts
    ];
}

/**
 * الحصول على القوالب المفلترة
 */
function getFilteredTemplates() {
    let templates = getAllTemplates();
    
    // فلترة حسب الفئة
    if (currentCategory !== 'all') {
        templates = templates.filter(template => template.category === currentCategory);
    }
    
    // فلترة حسب البحث
    const searchTerm = document.getElementById('templateSearch')?.value.toLowerCase() || '';
    if (searchTerm) {
        templates = templates.filter(template => 
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm)
        );
    }
    
    // فلترة حسب الفئة المحددة
    const categoryFilter = document.getElementById('categoryFilter')?.value || '';
    if (categoryFilter) {
        templates = templates.filter(template => template.category === categoryFilter);
    }
    
    // فلترة حسب الحالة
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    if (statusFilter) {
        templates = templates.filter(template => template.status === statusFilter);
    }
    
    return templates;
}

/**
 * تبديل الفئة
 */
function switchCategory(category) {
    currentCategory = category;
    
    // تحديث التبويبات
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');
    
    // إعادة تحميل القوالب
    loadTemplates();
}

/**
 * فلترة القوالب
 */
function filterTemplates() {
    loadTemplates();
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    document.getElementById('templateSearch').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    currentCategory = 'all';
    
    // إعادة تعيين التبويبات
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector('[data-category="all"]').classList.add('active');
    
    loadTemplates();
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // البحث المباشر
    const searchInput = document.getElementById('templateSearch');
    if (searchInput) {
        searchInput.addEventListener('input', filterTemplates);
    }
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, targetValue, duration = 1000) {
    if (!element) return;
    
    const startValue = 0;
    const increment = targetValue / (duration / 16);
    let currentValue = startValue;
    
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }
        element.textContent = Math.floor(currentValue);
    }, 16);
}

// ===== وظائف القوالب =====

/**
 * إنشاء قالب جديد
 */
function createNewTemplate() {
    showNotification('سيتم فتح معالج إنشاء القوالب قريباً', 'info');
    // TODO: فتح معالج إنشاء القوالب
}

/**
 * فتح محرر القوالب
 */
function openTemplateEditor() {
    showNotification('جاري فتح محرر القوالب...', 'info');

    // إضافة تأثير انتقال سلس
    setTimeout(() => {
        window.location.href = 'template-editor.html';
    }, 500);
}

/**
 * استيراد قالب
 */
function importTemplate() {
    showNotification('سيتم فتح نافذة استيراد القوالب قريباً', 'info');
    // TODO: فتح نافذة استيراد القوالب
}

/**
 * فتح قالب
 */
function openTemplate(templateId) {
    showNotification(`جاري فتح القالب: ${templateId}`, 'info');

    // إضافة تأثير انتقال سلس
    setTimeout(() => {
        window.location.href = `template-editor.html?template=${templateId}&mode=view`;
    }, 500);
}

/**
 * تحرير قالب
 */
function editTemplate(templateId) {
    showNotification(`جاري فتح محرر القالب: ${templateId}`, 'info');

    // إضافة تأثير انتقال سلس
    setTimeout(() => {
        window.location.href = `template-editor.html?template=${templateId}&mode=edit`;
    }, 500);
}

/**
 * معاينة قالب
 */
function previewTemplate(templateId) {
    showNotification(`سيتم معاينة القالب: ${templateId}`, 'info');
    // TODO: فتح نافذة المعاينة
}

/**
 * نسخ قالب
 */
function duplicateTemplate(templateId) {
    showNotification(`سيتم نسخ القالب: ${templateId}`, 'success');
    // TODO: إنشاء نسخة من القالب
}

// ===== الأدوات السريعة =====

/**
 * فتح معالج القوالب
 */
function openTemplateWizard() {
    showNotification('سيتم فتح معالج القوالب قريباً', 'info');
}

/**
 * فتح المحرر المجمع
 */
function openBulkEditor() {
    showNotification('سيتم فتح المحرر المجمع قريباً', 'info');
}

/**
 * تصدير القوالب
 */
function exportTemplates() {
    showNotification('سيتم تصدير القوالب قريباً', 'info');
}

/**
 * فتح مكتبة القوالب
 */
function openTemplateLibrary() {
    showNotification('سيتم فتح مكتبة القوالب قريباً', 'info');
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    // استخدام نظام الإشعارات الموجود
    if (window.showAdvancedNotification) {
        window.showAdvancedNotification(message, type);
    } else {
        alert(message);
    }
}

/* تحسينات واجهة المستخدم لنظام المبيعات */

/* الرسوم المتحركة العامة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    animation: fadeIn 0.6s ease-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 20px;
}

.card-body {
    padding: 25px;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 12px 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
}

/* تحسينات الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 700;
    color: #495057;
    padding: 20px 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    border: none;
    border-bottom: 1px solid #f1f3f4;
}

/* تحسينات النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تحسينات النوافذ المنبثقة */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: fadeIn 0.3s ease-out;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    border: none;
    padding: 25px;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    border: none;
    padding: 20px 30px;
}

/* تحسينات الشارات */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 600;
    font-size: 12px;
    animation: pulse 2s infinite;
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    animation: slideIn 0.5s ease-out;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تحسينات شريط التقدم */
.progress {
    height: 10px;
    border-radius: 10px;
    background-color: #f1f3f4;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
    background: linear-gradient(90deg, #007bff, #0056b3);
    animation: shimmer 2s infinite;
    background-size: 200px 100%;
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

/* تحسينات الأيقونات */
.fas, .far {
    transition: all 0.3s ease;
}

.card-header .fas {
    animation: bounce 2s infinite;
}

/* تحسينات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    animation: fadeIn 0.8s ease-out;
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    animation: pulse 3s infinite;
}

.stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* تحسينات التنقل */
.nav-pills .nav-link {
    border-radius: 15px;
    padding: 12px 20px;
    margin: 0 5px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

/* تحسينات الخلفية */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

/* تأثيرات خاصة */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.shadow-lg {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* تأثيرات التفاعل */
.interactive:hover {
    cursor: pointer;
    transform: scale(1.02);
    transition: all 0.3s ease;
}

.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* تحسينات النصوص */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تأثيرات الظلال */
.shadow-soft {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.shadow-hover:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

/**
 * ===================================
 * تحسينات التفاعل وتجربة المستخدم
 * Interactive Enhancements & User Experience
 * ===================================
 */

/* ===== تأثيرات التحميل والانتقالات ===== */

/* تأثير التحميل العام */
.loading-animation {
    position: relative;
    overflow: hidden;
}

.loading-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تأثيرات الظهور التدريجي */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.fade-in-down {
    opacity: 0;
    transform: translateY(-30px);
    animation: fadeInDown 0.6s ease forwards;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    animation: fadeInLeft 0.6s ease forwards;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 0.6s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأثيرات التكبير */
.scale-in {
    opacity: 0;
    transform: scale(0.8);
    animation: scaleIn 0.5s ease forwards;
}

@keyframes scaleIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== تأثيرات التمرير المحسنة ===== */

/* تأثير التمرير على البطاقات */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* تأثير التمرير على الأزرار */
.hover-glow {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.hover-glow:hover::before {
    left: 100%;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* تأثير النبض */
.pulse-effect {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* ===== تأثيرات النقر والتفاعل ===== */

/* تأثير الموجة عند النقر */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تأثير الضغط */
.press-effect {
    transition: transform 0.1s ease;
}

.press-effect:active {
    transform: scale(0.95);
}

/* ===== تحسينات إمكانية الوصول ===== */

/* تحسين التركيز بلوحة المفاتيح */
.focus-visible {
    outline: 2px solid var(--accounting-secondary);
    outline-offset: 2px;
    border-radius: 4px;
}

/* تحسين التباين للنصوص */
.high-contrast {
    color: #000000 !important;
    background: #ffffff !important;
}

/* تحسين حجم النص للقراءة */
.large-text {
    font-size: 1.2em !important;
    line-height: 1.6 !important;
}

/* ===== تأثيرات التحميل المتقدمة ===== */

/* مؤشر التحميل الدوار */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.3);
    border-top: 4px solid var(--accounting-secondary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* مؤشر التحميل النقطي */
.dots-loader {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.dots-loader div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: var(--accounting-secondary);
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.dots-loader div:nth-child(1) {
    left: 8px;
    animation: dots1 0.6s infinite;
}

.dots-loader div:nth-child(2) {
    left: 8px;
    animation: dots2 0.6s infinite;
}

.dots-loader div:nth-child(3) {
    left: 32px;
    animation: dots2 0.6s infinite;
}

.dots-loader div:nth-child(4) {
    left: 56px;
    animation: dots3 0.6s infinite;
}

@keyframes dots1 {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

@keyframes dots3 {
    0% { transform: scale(1); }
    100% { transform: scale(0); }
}

@keyframes dots2 {
    0% { transform: translate(0, 0); }
    100% { transform: translate(24px, 0); }
}

/* ===== تأثيرات الإشعارات ===== */

/* إشعارات منبثقة */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 1rem 1.5rem;
    z-index: 9999;
    transform: translateX(100%);
    animation: slideInRight 0.3s ease forwards;
}

.notification-toast.success {
    border-left: 4px solid var(--accounting-profit);
}

.notification-toast.error {
    border-left: 4px solid var(--accounting-loss);
}

.notification-toast.warning {
    border-left: 4px solid var(--accounting-pending);
}

.notification-toast.info {
    border-left: 4px solid var(--accounting-secondary);
}

@keyframes slideInRight {
    to { transform: translateX(0); }
}

@keyframes slideOutRight {
    to { transform: translateX(100%); }
}

/* ===== تأثيرات التمرير المتقدمة ===== */

/* شريط التقدم للتمرير */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 4px;
    background: var(--accounting-main-gradient);
    z-index: 9999;
    transition: width 0.1s ease;
}

/* تأثير الظهور عند التمرير */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== تحسينات الأداء ===== */

/* تحسين الرسوم المتحركة */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== تأثيرات خاصة للنظام المحاسبي ===== */

/* تأثير العد التصاعدي للأرقام */
.counting-animation {
    font-variant-numeric: tabular-nums;
    transition: all 0.3s ease;
}

/* تأثير التمييز للأرقام المهمة */
.highlight-number {
    position: relative;
    display: inline-block;
}

.highlight-number::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accounting-main-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.highlight-number:hover::after {
    transform: scaleX(1);
}

/* تأثير التحديث للبيانات */
.data-update {
    animation: dataUpdate 0.5s ease;
}

@keyframes dataUpdate {
    0% { background-color: rgba(102, 126, 234, 0.2); }
    100% { background-color: transparent; }
}

/* ===== تحسينات للأجهزة المحمولة ===== */

/* تحسين اللمس */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
        transform: none;
    }
    
    .hover-lift:active {
        transform: translateY(-4px) scale(1.01);
    }
    
    .hover-glow:hover {
        box-shadow: none;
    }
    
    .hover-glow:active {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .pulse-effect {
        animation: none;
    }
    
    .loading-animation::before {
        animation: none;
    }
}

/* ===================================
   Pages - صفحات النظام
   =================================== */

/* ===== لوحة التحكم الرئيسية ===== */
.dashboard-container {
  padding: var(--spacing-xl) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.page-header h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header .lead {
  font-size: var(--font-size-xl);
  color: var(--text-muted);
  font-weight: var(--font-weight-normal);
}

/* بطاقات الوحدات الرئيسية */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
}

.module-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-all);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: var(--backdrop-blur);
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: var(--transition-all);
}

.module-card:hover::before {
  transform: scaleX(1);
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.module-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  color: var(--white);
  transition: var(--transition-all);
}

.module-card:hover .module-icon {
  transform: scale(1.1) rotate(5deg);
}

.module-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.module-description {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.module-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.stat-number {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

/* الإجراءات السريعة */
.quick-actions {
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--shadow-md);
}

.quick-actions-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color-light);
}

.quick-actions-icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-warning);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
}

.quick-actions-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  background: var(--white);
  border: 2px solid var(--border-color-light);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-all);
  text-decoration: none;
  color: var(--text-primary);
}

.quick-action-btn:hover {
  border-color: var(--primary-color);
  background: rgba(var(--primary-rgb), 0.05);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  color: var(--primary-color);
}

.quick-action-icon {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
  transition: var(--transition-all);
}

.quick-action-btn:hover .quick-action-icon {
  transform: scale(1.2);
}

.quick-action-text {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
}

/* الإحصائيات */
.stats-section {
  margin-bottom: var(--spacing-3xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.stat-card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-all);
  backdrop-filter: var(--backdrop-blur);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card.primary::before { background: var(--gradient-primary); }
.stat-card.success::before { background: var(--gradient-success); }
.stat-card.danger::before { background: var(--gradient-danger); }
.stat-card.warning::before { background: var(--gradient-warning); }
.stat-card.info::before { background: var(--gradient-info); }

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: var(--white);
  flex-shrink: 0;
}

.stat-card.primary .stat-icon { background: var(--gradient-primary); }
.stat-card.success .stat-icon { background: var(--gradient-success); }
.stat-card.danger .stat-icon { background: var(--gradient-danger); }
.stat-card.warning .stat-icon { background: var(--gradient-warning); }
.stat-card.info .stat-icon { background: var(--gradient-info); }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-trend {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-top: var(--spacing-xs);
}

.stat-trend.up {
  color: var(--success-color);
}

.stat-trend.down {
  color: var(--danger-color);
}

/* النشاط الأخير */
.activity-section {
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--shadow-md);
}

.activity-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color-light);
}

.activity-icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-info);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
}

.activity-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: var(--transition-all);
}

.activity-item:hover {
  background: rgba(var(--primary-rgb), 0.05);
  transform: translateX(4px);
}

.activity-item-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--white);
  flex-shrink: 0;
}

.activity-item-icon.primary { background: var(--primary-color); }
.activity-item-icon.success { background: var(--success-color); }
.activity-item-icon.warning { background: var(--warning-color); }
.activity-item-icon.info { background: var(--info-color); }

.activity-item-content {
  flex: 1;
}

.activity-item-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-item-time {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* ===== صفحات إدارة البيانات ===== */
.page-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  backdrop-filter: var(--backdrop-blur);
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.title-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
  color: var(--white);
}

.title-icon.primary { background: var(--gradient-primary); }
.title-icon.success { background: var(--gradient-success); }
.title-icon.info { background: var(--gradient-info); }
.title-icon.warning { background: var(--gradient-warning); }

.title-text h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.title-text p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: var(--spacing-md);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-all);
  cursor: pointer;
  text-decoration: none;
}

.action-button.primary {
  background: var(--gradient-primary);
  color: var(--white);
}

.action-button.secondary {
  background: var(--gray-500);
  color: var(--white);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== الجداول المتقدمة ===== */
.advanced-table-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: var(--border-width) solid var(--border-color-light);
}

.table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.table-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.table-search {
  position: relative;
  max-width: 300px;
}

.table-search input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 40px;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
}

.table-search .search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.advanced-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
}

.advanced-table thead {
  background: var(--gradient-primary);
}

.advanced-table th {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: right;
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  font-size: var(--font-size-sm);
  border: none;
  position: relative;
}

.advanced-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.advanced-table th.sortable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.advanced-table th .sort-icon {
  margin-right: var(--spacing-xs);
  opacity: 0.5;
  transition: var(--transition-all);
}

.advanced-table th.sorted .sort-icon {
  opacity: 1;
}

.advanced-table tbody tr {
  transition: var(--transition-all);
  border-bottom: var(--border-width) solid var(--border-color-light);
}

.advanced-table tbody tr:hover {
  background: rgba(var(--primary-rgb), 0.05);
}

.advanced-table tbody tr:last-child {
  border-bottom: none;
}

.advanced-table td {
  padding: var(--spacing-md) var(--spacing-lg);
  vertical-align: middle;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.table-cell-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
}

.cell-action {
  padding: var(--spacing-xs);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-all);
  font-size: var(--font-size-sm);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell-action.edit {
  background: var(--info-color);
  color: var(--white);
}

.cell-action.delete {
  background: var(--danger-color);
  color: var(--white);
}

.cell-action.view {
  background: var(--success-color);
  color: var(--white);
}

.cell-action:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-sm);
}

/* حالات الجدول */
.table-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
}

.table-status.active {
  background: rgba(var(--success-rgb), 0.1);
  color: var(--success-color);
}

.table-status.inactive {
  background: rgba(var(--danger-rgb), 0.1);
  color: var(--danger-color);
}

.table-status.pending {
  background: rgba(var(--warning-rgb), 0.1);
  color: var(--warning-color);
}

.table-status.processing {
  background: rgba(var(--info-rgb), 0.1);
  color: var(--info-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* ترقيم الصفحات */
.table-pagination {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: var(--border-width) solid var(--border-color-light);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.pagination-controls {
  display: flex;
  gap: var(--spacing-xs);
  margin-right: auto;
}

.pagination-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: var(--border-width) solid var(--border-color);
  background: var(--white);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-all);
  font-size: var(--font-size-sm);
  min-width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: var(--opacity-50);
  cursor: not-allowed;
}

.pagination-btn:disabled:hover {
  background: var(--white);
  color: var(--text-primary);
  border-color: var(--border-color);
}

/* ===== النماذج المتقدمة ===== */
.advanced-form {
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.form-header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-xl);
  text-align: center;
}

.form-header h2 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.form-header p {
  margin: 0;
  opacity: 0.9;
  font-size: var(--font-size-base);
}

.form-body {
  padding: var(--spacing-xl);
}

.form-section {
  margin-bottom: var(--spacing-xl);
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-color-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.form-section-icon {
  color: var(--primary-color);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group-inline {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.form-label-required::after {
  content: ' *';
  color: var(--danger-color);
}

.form-input-group {
  position: relative;
}

.form-input-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.form-input-group input {
  padding-right: 40px;
}

.form-help {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-footer {
  background: var(--bg-secondary);
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  border-top: var(--border-width) solid var(--border-color-light);
}

/* ===== الفلاتر المتقدمة ===== */
.filters-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
}

.filters-header {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-bottom: var(--border-width) solid var(--border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filters-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filters-toggle {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: var(--transition-all);
}

.filters-toggle:hover {
  color: var(--primary-dark);
}

.filters-body {
  padding: var(--spacing-lg);
  display: none;
}

.filters-body.show {
  display: block;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.filters-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: var(--border-width) solid var(--border-color-light);
}

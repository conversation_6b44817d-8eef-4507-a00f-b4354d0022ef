<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر القوالب - نظام قيمة الوعد</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../styles/main.css">
    <link rel="stylesheet" href="../css/accounting-enhanced.css">
    <link rel="stylesheet" href="../css/templates-management.css">
    <link rel="stylesheet" href="../css/template-editor.css">
</head>
<body>
    <!-- شريط التقدم للتمرير -->
    <div class="scroll-progress" id="scrollProgress"></div>
    
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../index.html">
                <i class="fas fa-plane me-2"></i>
                قيمة الوعد
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="templates-management.html">
                    <i class="fas fa-arrow-right me-1"></i>العودة للقوالب
                </a>
            </div>
            
            <div class="navbar-nav">
                <button class="btn btn-success me-2" onclick="saveTemplate()">
                    <i class="fas fa-save me-1"></i>حفظ
                </button>
                <button class="btn btn-outline-light me-2" onclick="previewTemplate()">
                    <i class="fas fa-eye me-1"></i>معاينة
                </button>
                <button class="btn btn-outline-info me-2" onclick="openAdvancedPreview()">
                    <i class="fas fa-external-link-alt me-1"></i>معاينة متقدمة
                </button>
                <button class="btn btn-outline-light" onclick="printTemplate()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="template-editor-container">
        <!-- شريط الأدوات -->
        <div class="editor-toolbar">
            <div class="toolbar-section">
                <h5><i class="fas fa-palette me-2"></i>التصميم</h5>
                <div class="toolbar-group">
                    <button class="tool-btn" onclick="addTextField()" title="إضافة نص">
                        <i class="fas fa-font"></i>
                    </button>
                    <button class="tool-btn" onclick="addImageField()" title="إضافة صورة">
                        <i class="fas fa-image"></i>
                    </button>
                    <button class="tool-btn" onclick="addTableField()" title="إضافة جدول">
                        <i class="fas fa-table"></i>
                    </button>
                    <button class="tool-btn" onclick="addLineField()" title="إضافة خط">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            
            <div class="toolbar-section">
                <h5><i class="fas fa-database me-2"></i>البيانات</h5>
                <div class="toolbar-group">
                    <button class="tool-btn" onclick="addDataField('company_name')" title="اسم الشركة">
                        <i class="fas fa-building"></i>
                    </button>
                    <button class="tool-btn" onclick="addDataField('date')" title="التاريخ">
                        <i class="fas fa-calendar"></i>
                    </button>
                    <button class="tool-btn" onclick="addDataField('amount')" title="المبلغ">
                        <i class="fas fa-dollar-sign"></i>
                    </button>
                    <button class="tool-btn" onclick="addDataField('customer')" title="العميل">
                        <i class="fas fa-user"></i>
                    </button>
                </div>
            </div>
            
            <div class="toolbar-section">
                <h5><i class="fas fa-cogs me-2"></i>الإعدادات</h5>
                <div class="toolbar-group">
                    <select class="form-select form-select-sm" onchange="changePageSize(this.value)">
                        <option value="a4">A4</option>
                        <option value="a5">A5</option>
                        <option value="letter">Letter</option>
                    </select>
                    <select class="form-select form-select-sm" onchange="changeOrientation(this.value)">
                        <option value="portrait">عمودي</option>
                        <option value="landscape">أفقي</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- منطقة العمل -->
        <div class="editor-workspace">
            <!-- لوحة الخصائص -->
            <div class="properties-panel">
                <h5><i class="fas fa-sliders-h me-2"></i>خصائص العنصر</h5>
                <div class="properties-content" id="propertiesContent">
                    <p class="text-muted">اختر عنصراً لتحرير خصائصه</p>
                </div>
            </div>

            <!-- منطقة التصميم -->
            <div class="design-area">
                <div class="template-canvas" id="templateCanvas">
                    <!-- رأس القالب الافتراضي -->
                    <div class="template-header editable-element" data-type="header">
                        <div class="company-logo">
                            <i class="fas fa-building fa-3x"></i>
                        </div>
                        <div class="company-info">
                            <h2 class="company-name" contenteditable="true">شركة قيمة الوعد للسفريات</h2>
                            <p class="company-details" contenteditable="true">
                                العنوان: المملكة العربية السعودية - الرياض<br>
                                الهاتف: +966 11 123 4567 | البريد: <EMAIL>
                            </p>
                        </div>
                    </div>

                    <!-- عنوان المستند -->
                    <div class="document-title editable-element" data-type="title">
                        <h3 contenteditable="true">سند قبض</h3>
                        <div class="document-number">
                            <span>رقم السند: </span>
                            <span class="data-field" data-field="voucher_number">[رقم السند]</span>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="customer-info editable-element" data-type="customer">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>العميل: </strong>
                                <span class="data-field" data-field="customer_name">[اسم العميل]</span>
                            </div>
                            <div class="col-md-6">
                                <strong>التاريخ: </strong>
                                <span class="data-field" data-field="date">[التاريخ]</span>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل المبلغ -->
                    <div class="amount-details editable-element" data-type="amount">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>البيان</th>
                                    <th>المبلغ</th>
                                    <th>العملة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="data-field" data-field="description">[البيان]</td>
                                    <td class="data-field" data-field="amount">[المبلغ]</td>
                                    <td class="data-field" data-field="currency">[العملة]</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- المبلغ بالأحرف -->
                    <div class="amount-words editable-element" data-type="words">
                        <strong>المبلغ بالأحرف: </strong>
                        <span class="data-field" data-field="amount_words">[المبلغ بالأحرف]</span>
                    </div>

                    <!-- التوقيعات -->
                    <div class="signatures editable-element" data-type="signatures">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="signature-box">
                                    <p>المحاسب</p>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box">
                                    <p>المدير المالي</p>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="signature-box">
                                    <p>العميل</p>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ذيل القالب -->
                    <div class="template-footer editable-element" data-type="footer">
                        <p class="text-center text-muted" contenteditable="true">
                            شكراً لتعاملكم معنا - نتطلع لخدمتكم دائماً
                        </p>
                    </div>
                </div>
            </div>

            <!-- لوحة العناصر -->
            <div class="elements-panel">
                <h5><i class="fas fa-layer-group me-2"></i>العناصر</h5>
                <div class="elements-list" id="elementsList">
                    <!-- سيتم ملء قائمة العناصر هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة المعاينة -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة القالب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="preview-content" id="previewContent">
                        <!-- سيتم عرض المعاينة هنا -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printPreview()">طباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../js/template-editor.js"></script>
</body>
</html>
